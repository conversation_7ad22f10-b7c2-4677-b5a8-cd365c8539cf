const express = require('express');
const router = express.Router();
const Payment = require('../models/Payment');
const User = require('../models/User');
const AffiliateTransaction = require('../models/Affiliate');
const { authenticate, isAdmin } = require('../middleware/auth');
const nodemailer = require('nodemailer');
require('dotenv').config();

// Configure email transporter
const transporter = nodemailer.createTransport({
  host: process.env.EMAIL_HOST,
  port: process.env.EMAIL_PORT,
  secure: process.env.EMAIL_SECURE === 'true',
  auth: {
    user: process.env.EMAIL_USER,
    pass: process.env.EMAIL_PASSWORD
  }
});

/**
 * Send license key to customer via email
 * @param {Object} payment - Payment object with customer info and license key
 * @returns {Promise} - Email sending result
 */
const sendLicenseKeyEmail = async (payment) => {
  try {
    const { customerInfo, licenseKey, plan, orderId } = payment;

    const mailOptions = {
      from: `"Meta Master" <${process.env.EMAIL_FROM}>`,
      to: customerInfo.email,
      subject: 'Your Meta Master License Key',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <h2 style="color: #333; text-align: center;">Thank You for Your Purchase!</h2>
          <p>Hello ${customerInfo.firstName} ${customerInfo.lastName},</p>
          <p>Your payment for Meta Master ${plan} plan has been verified and approved.</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0; text-align: center;">
            <h3 style="margin-top: 0;">Your License Key:</h3>
            <p style="font-family: monospace; font-size: 18px; background-color: #fff; padding: 10px; border-radius: 3px;">${licenseKey}</p>
          </div>
          <p>Order Details:</p>
          <ul>
            <li>Order ID: ${orderId}</li>
            <li>Plan: ${plan}</li>
          </ul>
          <p>To activate your software:</p>
          <ol>
            <li>Open Meta Master application</li>
            <li>Click on "Activate License"</li>
            <li>Enter your license key</li>
            <li>Click "Activate"</li>
          </ol>
          <p>If you have any questions or need assistance, please contact our support team.</p>
          <p>Thank you for choosing Meta Master!</p>
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e0e0e0; color: #777; font-size: 12px;">
            <p>© ${new Date().getFullYear()} Meta Master. All rights reserved.</p>
          </div>
        </div>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('License key email sent:', info.messageId);
    return info;
  } catch (error) {
    console.error('Error sending license key email:', error);
    throw error;
  }
};

/**
 * @route   POST /api/payments/verify
 * @desc    Verify and process a payment
 * @access  Public
 */
router.post('/verify', authenticate, async (req, res) => {
  try {
    const { transactionId, paymentMethod, amount, plan, customerInfo, affiliateId } = req.body;

    // Validate required fields
    if (!transactionId || !paymentMethod || !amount || !plan || !customerInfo) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields'
      });
    }

    // Validate customer info
    if (!customerInfo.firstName || !customerInfo.lastName || !customerInfo.email || !customerInfo.phone) {
      return res.status(400).json({
        success: false,
        message: 'Missing required customer information'
      });
    }

    // Generate order ID with format: MM-YEAR-RANDOMNUMBER
    const orderId = `MM-${new Date().getFullYear()}-${Math.floor(100000 + Math.random() * 900000)}`;

    // Get user from token
    const userId = req.user.id;

    // Check if an affiliate ID was provided
    let referrer = null;
    if (affiliateId) {
      // First try to find the referrer by username (new method)
      referrer = await User.findOne({ username: affiliateId });

      // If not found by username, try to find by affiliateId (backward compatibility)
      if (!referrer) {
        referrer = await User.findOne({ affiliateId });
      }

      // Make sure the referrer is not the same as the current user
      if (referrer && referrer._id.toString() === userId.toString()) {
        referrer = null; // Can't refer yourself
      }
    }

    // Create new payment record
    const payment = new Payment({
      orderId,
      transactionId,
      paymentMethod,
      amount,
      plan,
      user: userId,
      referredBy: referrer ? referrer._id : null,
      customerInfo,
      status: 'pending' // Initial status is pending
    });

    // In a real-world scenario, you would verify the transaction with the payment provider here
    // For demonstration purposes, we'll simulate a successful verification

    // Save the payment record without generating a temporary license key
    await payment.save();

    // Add a pending payment record to user's account without a license key
    const user = await User.findById(userId);
    if (user) {
      // If this user was referred, update their referredBy field
      if (referrer && !user.referredBy) {
        user.referredBy = referrer._id;
      }

      // The license key will be added by the admin when the payment is approved
      await user.addPendingPayment(plan, payment._id);
    }

    // Return success response with order details
    return res.status(200).json({
      success: true,
      message: 'Payment verified successfully',
      data: {
        orderId: payment.orderId,
        plan: payment.plan,
        amount: payment.amount,
        paymentMethod: payment.paymentMethod,
        status: payment.status
      }
    });
  } catch (error) {
    console.error('Payment verification error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment verification',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments/test
 * @desc    Test MongoDB connection
 * @access  Public
 */
router.get('/test', async (req, res) => {
  try {
    // Create a test payment object
    const testPayment = new Payment({
      orderId: `TEST-${Date.now()}`,
      transactionId: 'TEST123456789',
      paymentMethod: 'bkash',
      amount: 199,
      plan: 'Monthly',
      customerInfo: {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        phone: '01712345678'
      }
    });

    // We don't generate a license key for test payments anymore
    // License keys are only provided by admins

    // Don't save to database, just return the object
    return res.status(200).json({
      success: true,
      message: 'MongoDB connection test successful',
      data: testPayment
    });
  } catch (error) {
    console.error('MongoDB test error:', error);
    return res.status(500).json({
      success: false,
      message: 'MongoDB connection test failed',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments/:orderId
 * @desc    Get payment details by order ID
 * @access  Private (Admin only)
 */
router.get('/:orderId', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;

    // Find payment by order ID
    const payment = await Payment.findOne({ orderId });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Return payment details
    return res.status(200).json({
      success: true,
      data: {
        orderId: payment.orderId,
        plan: payment.plan,
        amount: payment.amount,
        paymentMethod: payment.paymentMethod,
        status: payment.status,
        createdAt: payment.createdAt
      }
    });
  } catch (error) {
    console.error('Get payment error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving payment',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments
 * @desc    Get all payments
 * @access  Private (Admin only)
 */
router.get('/', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all payments, sorted by creation date (newest first)
    const payments = await Payment.find().sort({ createdAt: -1 });

    return res.status(200).json({
      success: true,
      count: payments.length,
      data: payments
    });
  } catch (error) {
    console.error('Get all payments error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving payments',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/payments/:orderId/approve
 * @desc    Approve a payment and send license key
 * @access  Private (Admin only)
 */
router.post('/:orderId/approve', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { adminNotes, licenseKey } = req.body;

    // Find payment by order ID
    const payment = await Payment.findOne({ orderId });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if payment is already verified
    if (payment.verificationStatus === 'approved') {
      return res.status(400).json({
        success: false,
        message: 'Payment already approved'
      });
    }

    // Require a license key for approval
    if (!licenseKey || licenseKey.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'License key is required for payment approval'
      });
    }

    // Use the provided license key
    payment.licenseKey = licenseKey.trim();

    // Update payment status
    payment.verificationStatus = 'approved';
    payment.status = 'verified';
    payment.adminNotes = adminNotes || '';
    payment.verifiedBy = req.user.id;
    payment.verifiedAt = new Date();

    // Save the payment record
    await payment.save();

    // Add or update license key in user's account if payment is linked to a user
    if (payment.user) {
      const user = await User.findById(payment.user);
      if (user) {
        // Check if a license key entry already exists for this payment
        const existingLicenseIndex = user.licenseKeys.findIndex(
          license => license.paymentId && license.paymentId.toString() === payment._id.toString()
        );

        if (existingLicenseIndex !== -1) {
          // Update existing license entry with the admin-provided key
          user.licenseKeys[existingLicenseIndex].key = payment.licenseKey;
          user.licenseKeys[existingLicenseIndex].status = 'active';
          user.licenseKeys[existingLicenseIndex].paymentStatus = 'approved';
          await user.save();
        } else {
          // Add new license key (this is a fallback, normally there should be a pending entry)
          await user.addLicenseKey(payment.licenseKey, payment.plan, payment._id, 'approved');
        }
      }
    }

    // Process affiliate commission if this payment was referred by someone
    if (payment.referredBy) {
      try {
        const referrer = await User.findById(payment.referredBy);
        if (referrer) {
          // Calculate 20% commission
          const commissionRate = 0.2; // 20%
          const commissionAmount = payment.amount * commissionRate;

          // Create affiliate transaction record
          const affiliateTransaction = new AffiliateTransaction({
            referrer: referrer._id,
            referredUser: payment.user,
            payment: payment._id,
            originalAmount: payment.amount,
            commissionAmount: commissionAmount,
            commissionRate: commissionRate,
            status: 'approved',
            plan: payment.plan
          });

          await affiliateTransaction.save();

          // Update referrer's commission balance
          referrer.affiliateCommission += commissionAmount;
          await referrer.save();

          // Mark payment as commission paid
          payment.isAffiliateCommissionPaid = true;
          await payment.save();

          console.log(`Affiliate commission of ${commissionAmount} added to user ${referrer._id} for payment ${payment.orderId}`);
        }
      } catch (commissionError) {
        console.error('Error processing affiliate commission:', commissionError);
        // Continue with the response even if commission processing fails
      }
    }

    // Send license key email
    try {
      await sendLicenseKeyEmail(payment);
      payment.licenseKeySent = true;
      await payment.save();
    } catch (emailError) {
      console.error('Failed to send license key email:', emailError);
      // Continue with the response even if email fails
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Payment approved and license key generated',
      data: {
        orderId: payment.orderId,
        plan: payment.plan,
        licenseKey: payment.licenseKey,
        status: payment.status,
        verificationStatus: payment.verificationStatus,
        licenseKeySent: payment.licenseKeySent
      }
    });
  } catch (error) {
    console.error('Payment approval error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment approval',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/payments/:orderId/reject
 * @desc    Reject a payment
 * @access  Private (Admin only)
 */
router.post('/:orderId/reject', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { adminNotes } = req.body;

    // Find payment by order ID
    const payment = await Payment.findOne({ orderId });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if payment is already verified or rejected
    if (payment.verificationStatus !== 'pending') {
      return res.status(400).json({
        success: false,
        message: `Payment already ${payment.verificationStatus}`
      });
    }

    // Update payment status
    payment.verificationStatus = 'rejected';
    payment.status = 'rejected';
    payment.adminNotes = adminNotes || 'Payment rejected by admin';
    payment.verifiedBy = req.user.id;
    payment.verifiedAt = new Date();

    // Save the payment record
    await payment.save();

    // Update license key status in user's account if payment is linked to a user
    if (payment.user) {
      const user = await User.findById(payment.user);
      if (user) {
        // Find the license key entry for this payment
        const existingLicenseIndex = user.licenseKeys.findIndex(
          license => license.paymentId && license.paymentId.toString() === payment._id.toString()
        );

        if (existingLicenseIndex !== -1) {
          // Update license status to rejected
          user.licenseKeys[existingLicenseIndex].status = 'revoked';
          user.licenseKeys[existingLicenseIndex].paymentStatus = 'rejected';
          await user.save();
        }
      }
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'Payment rejected',
      data: {
        orderId: payment.orderId,
        status: payment.status,
        verificationStatus: payment.verificationStatus
      }
    });
  } catch (error) {
    console.error('Payment rejection error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during payment rejection',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/payments/:orderId/resend-license
 * @desc    Resend license key email
 * @access  Private (Admin only)
 */
router.post('/:orderId/resend-license', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;

    // Find payment by order ID
    const payment = await Payment.findOne({ orderId });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Check if payment is approved and has a license key
    if (payment.verificationStatus !== 'approved' || !payment.licenseKey) {
      return res.status(400).json({
        success: false,
        message: 'Payment not approved or missing license key'
      });
    }

    // Send license key email
    try {
      await sendLicenseKeyEmail(payment);
      payment.licenseKeySent = true;
      await payment.save();
    } catch (emailError) {
      console.error('Failed to send license key email:', emailError);
      return res.status(500).json({
        success: false,
        message: 'Failed to send license key email',
        error: process.env.NODE_ENV === 'development' ? emailError.message : {}
      });
    }

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'License key email sent successfully',
      data: {
        orderId: payment.orderId,
        email: payment.customerInfo.email
      }
    });
  } catch (error) {
    console.error('Resend license key error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during license key resend',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   GET /api/payments/pending
 * @desc    Get all pending payments
 * @access  Private (Admin only)
 */
router.get('/pending', authenticate, isAdmin, async (req, res) => {
  try {
    // Get all pending payments, sorted by creation date (newest first)
    const payments = await Payment.find({ verificationStatus: 'pending' }).sort({ createdAt: -1 });

    return res.status(200).json({
      success: true,
      count: payments.length,
      data: payments
    });
  } catch (error) {
    console.error('Get pending payments error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error while retrieving pending payments',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

/**
 * @route   POST /api/payments/:orderId/update-license
 * @desc    Manually update a payment's license key
 * @access  Private (Admin only)
 */
router.post('/:orderId/update-license', authenticate, isAdmin, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { licenseKey } = req.body;

    // Validate license key
    if (!licenseKey || licenseKey.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'License key is required'
      });
    }

    // Find payment by order ID
    const payment = await Payment.findOne({ orderId });

    if (!payment) {
      return res.status(404).json({
        success: false,
        message: 'Payment not found'
      });
    }

    // Update the license key
    payment.licenseKey = licenseKey;

    // If payment is already approved, update the license key for the user as well
    if (payment.verificationStatus === 'approved' && payment.user) {
      const user = await User.findById(payment.user);
      if (user) {
        // Find the license key entry for this payment
        const licenseIndex = user.licenseKeys.findIndex(
          license => license.paymentId && license.paymentId.toString() === payment._id.toString()
        );

        if (licenseIndex !== -1) {
          // Update the license key
          user.licenseKeys[licenseIndex].key = licenseKey;
          await user.save();
        }
      }
    }

    // Save the payment record
    await payment.save();

    // Return success response
    return res.status(200).json({
      success: true,
      message: 'License key updated successfully',
      data: {
        orderId: payment.orderId,
        licenseKey: payment.licenseKey
      }
    });
  } catch (error) {
    console.error('Update license key error:', error);
    return res.status(500).json({
      success: false,
      message: 'Server error during license key update',
      error: process.env.NODE_ENV === 'development' ? error.message : {}
    });
  }
});

module.exports = router;
