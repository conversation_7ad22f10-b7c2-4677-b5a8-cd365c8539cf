import { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

export default function RegisterPage() {
  const { register, handleSubmit, formState: { errors }, watch } = useForm()
  const [isLoading, setIsLoading] = useState(false)
  const [registerError, setRegisterError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  // Get redirect information from location state (if coming from checkout)
  const { returnTo, plan, price } = location.state || {}

  const password = watch('password', '')

  useEffect(() => {
    // Check if already logged in
    const token = localStorage.getItem('userToken')
    if (token) {
      // If coming from checkout, redirect back there
      if (returnTo === '/checkout' && plan && price) {
        navigate('/checkout', { state: { plan, price } })
      } else {
        navigate('/user/dashboard')
      }
    }
  }, [navigate, returnTo, plan, price])

  const onSubmit = async (data) => {
    setIsLoading(true)
    setRegisterError('')

    try {
      const response = await fetch('/api/users/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: data.username,
          email: data.email,
          password: data.password,
          name: data.name
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.message || 'Registration failed')
      }

      // Store token in localStorage
      localStorage.setItem('userToken', result.token)

      // Redirect to checkout if coming from there, otherwise to dashboard
      if (returnTo === '/checkout' && plan && price) {
        navigate('/checkout', { state: { plan, price } })
      } else {
        navigate('/user/dashboard')
      }
    } catch (error) {
      console.error('Registration error:', error)
      setRegisterError(error.message || 'Registration failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <section className="py-16 md:py-24 bg-dark min-h-screen">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md mx-auto bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-2xl p-8"
        >
          <div className="flex justify-end mb-4">
            <Link
              to="/"
              className="text-primary hover:text-secondary transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              Go to Home
            </Link>
          </div>

          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Create an Account
            </h1>
            <p className="text-text mt-2">
              Join Meta Master to manage your licenses and purchases
            </p>
          </div>

          {registerError && (
            <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4 mb-6">
              {registerError}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-light mb-1">
                Name
              </label>
              <input
                type="text"
                id="name"
                className={`w-full bg-dark border ${errors.name ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                {...register('name', { required: 'Name is required' })}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-danger">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="username" className="block text-sm font-medium text-light mb-1">
                Username
              </label>
              <input
                type="text"
                id="username"
                className={`w-full bg-dark border ${errors.username ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                {...register('username', { required: 'Username is required' })}
              />
              {errors.username && (
                <p className="mt-1 text-sm text-danger">{errors.username.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-light mb-1">
                Email Address
              </label>
              <input
                type="email"
                id="email"
                className={`w-full bg-dark border ${errors.email ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                {...register('email', {
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address'
                  }
                })}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-danger">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-light mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  className={`w-full bg-dark border ${errors.password ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                  {...register('password', {
                    required: 'Password is required',
                    minLength: {
                      value: 6,
                      message: 'Password must be at least 6 characters'
                    }
                  })}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text hover:text-light"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-danger">{errors.password.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-light mb-1">
                Confirm Password
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? 'text' : 'password'}
                  id="confirmPassword"
                  className={`w-full bg-dark border ${errors.confirmPassword ? 'border-danger' : 'border-border'} rounded-lg px-4 py-3 text-light focus:outline-none focus:border-primary transition-colors`}
                  {...register('confirmPassword', {
                    required: 'Please confirm your password',
                    validate: value => value === password || 'Passwords do not match'
                  })}
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text hover:text-light"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-danger">{errors.confirmPassword.message}</p>
              )}
            </div>



            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-primary to-secondary text-white font-medium py-3 px-4 rounded-lg hover:opacity-90 transition-opacity disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Creating Account...' : 'Create Account'}
            </button>

            <div className="text-center text-sm text-text">
              Already have an account?{' '}
              <Link to="/user/login" className="text-primary hover:underline">
                Log In
              </Link>
            </div>
          </form>
        </motion.div>
      </div>
    </section>
  )
}
