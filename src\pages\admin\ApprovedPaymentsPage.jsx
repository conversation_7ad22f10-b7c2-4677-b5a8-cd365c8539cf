import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import { EyeIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

const ApprovedPaymentsPage = () => {
  const [payments, setPayments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [editLicenseKey, setEditLicenseKey] = useState(false);
  const [newLicenseKey, setNewLicenseKey] = useState('');
  const [updateLicenseLoading, setUpdateLicenseLoading] = useState(false);

  useEffect(() => {
    fetchApprovedPayments();
  }, []);

  const fetchApprovedPayments = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payments`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }

      const data = await response.json();
      // Filter only approved payments
      const approvedPayments = data.data.filter(payment => payment.verificationStatus === 'approved');
      setPayments(approvedPayments);
    } catch (err) {
      console.error('Error fetching approved payments:', err);
      setError('Failed to load approved payments. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = (payment) => {
    setSelectedPayment(payment);
    setNewLicenseKey(payment.licenseKey || '');
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
    setEditLicenseKey(false);
    setNewLicenseKey('');
  };

  const handleResendLicense = async (orderId) => {
    setResendLoading(true);

    try {
      const token = localStorage.getItem('adminToken');

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payments/${orderId}/resend-license`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to resend license key');
      }

      // Show success message
      alert('License key email sent successfully');

      // Refresh the payments list
      await fetchApprovedPayments();
    } catch (err) {
      console.error('Error resending license key:', err);
      alert('Failed to resend license key. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  const handleUpdateLicenseKey = async () => {
    if (!selectedPayment || !newLicenseKey.trim()) {
      alert('Please enter a valid license key');
      return;
    }

    setUpdateLicenseLoading(true);

    try {
      const token = localStorage.getItem('adminToken');

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payments/${selectedPayment.orderId}/update-license`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ licenseKey: newLicenseKey.trim() })
      });

      if (!response.ok) {
        throw new Error('Failed to update license key');
      }

      // Show success message
      alert('License key updated successfully');

      // Refresh the payments list
      await fetchApprovedPayments();

      // Update the selected payment with the new license key
      setSelectedPayment({
        ...selectedPayment,
        licenseKey: newLicenseKey.trim()
      });

      // Exit edit mode
      setEditLicenseKey(false);
      setNewLicenseKey('');
    } catch (err) {
      console.error('Error updating license key:', err);
      alert('Failed to update license key. Please try again.');
    } finally {
      setUpdateLicenseLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <AdminLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold text-gray-900">Approved Payments</h1>
        <p className="mt-1 text-sm text-gray-500">
          View all approved payments and resend license keys if needed
        </p>

        {isLoading ? (
          <div className="flex justify-center mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="mt-8 bg-red-50 border-l-4 border-red-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        ) : payments.length === 0 ? (
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
            <p className="text-gray-500">No approved payments found</p>
          </div>
        ) : (
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {payments.map((payment) => (
                <li key={payment.orderId}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-blue-600 truncate">
                          {payment.orderId}
                        </p>
                        <p className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Approved
                        </p>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <button
                          onClick={() => handleViewDetails(payment)}
                          className="mr-2 px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <EyeIcon className="h-4 w-4 inline mr-1" />
                          Details
                        </button>
                        <button
                          onClick={() => handleResendLicense(payment.orderId)}
                          disabled={resendLoading}
                          className="px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                        >
                          <PaperAirplaneIcon className="h-4 w-4 inline mr-1" />
                          Resend License
                        </button>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          {payment.customerInfo.firstName} {payment.customerInfo.lastName}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          {payment.paymentMethod.toUpperCase()} - ৳{payment.amount}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          {payment.plan}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          Approved on {payment.verifiedAt ? formatDate(payment.verifiedAt) : formatDate(payment.updatedAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Modal for payment details */}
      {isModalOpen && selectedPayment && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      Payment Details
                    </h3>

                    <div className="mt-4 border-t border-gray-200 pt-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Order ID</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.orderId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Transaction ID</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.transactionId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Payment Method</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.paymentMethod.toUpperCase()}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Amount</p>
                          <p className="mt-1 text-sm text-gray-900">৳{selectedPayment.amount}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Plan</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.plan}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Date</p>
                          <p className="mt-1 text-sm text-gray-900">{formatDate(selectedPayment.createdAt)}</p>
                        </div>
                      </div>

                      <div className="mt-4 border border-green-200 bg-green-50 rounded-lg p-4">
                        <div className="flex justify-between items-center">
                          <p className="text-sm font-medium text-green-700 flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                            </svg>
                            License Key (Visible to User)
                          </p>
                          {!editLicenseKey && (
                            <button
                              onClick={() => {
                                setEditLicenseKey(true);
                                setNewLicenseKey(selectedPayment.licenseKey || '');
                              }}
                              className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200"
                            >
                              Edit License Key
                            </button>
                          )}
                        </div>

                        {editLicenseKey ? (
                          <div className="mt-1">
                            <div className="flex items-center">
                              <input
                                type="text"
                                value={newLicenseKey}
                                onChange={(e) => setNewLicenseKey(e.target.value)}
                                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                                placeholder="Enter new license key"
                              />
                              <button
                                type="button"
                                onClick={() => {
                                  // Generate a random license key format: XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
                                  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                                  let generatedKey = '';
                                  for (let i = 0; i < 5; i++) {
                                    for (let j = 0; j < 5; j++) {
                                      generatedKey += characters.charAt(Math.floor(Math.random() * characters.length));
                                    }
                                    if (i < 4) generatedKey += '-';
                                  }
                                  setNewLicenseKey(generatedKey);
                                }}
                                className="ml-2 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                              >
                                Generate
                              </button>
                            </div>
                            <div className="mt-2 flex justify-end space-x-2">
                              <button
                                onClick={() => {
                                  setEditLicenseKey(false);
                                  setNewLicenseKey('');
                                }}
                                className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                                disabled={updateLicenseLoading}
                              >
                                Cancel
                              </button>
                              <button
                                onClick={handleUpdateLicenseKey}
                                className="px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                                disabled={updateLicenseLoading}
                              >
                                {updateLicenseLoading ? (
                                  <>
                                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Updating...
                                  </>
                                ) : (
                                  'Update'
                                )}
                              </button>
                            </div>
                          </div>
                        ) : (
                          <div className="flex items-center mt-3">
                            <div className="flex-grow">
                              <p className="text-xs text-green-600 mb-1">This license key is shown in the user's dashboard:</p>
                              <p className="text-sm font-mono bg-white border border-green-300 p-3 rounded-md flex-grow text-green-800 font-semibold tracking-wider">{selectedPayment.licenseKey}</p>
                            </div>
                            <button
                              onClick={() => {
                                navigator.clipboard.writeText(selectedPayment.licenseKey);
                                alert('License key copied to clipboard');
                              }}
                              className="ml-2 px-3 py-2 border border-green-300 rounded-md text-sm font-medium text-green-700 bg-white hover:bg-green-50 h-fit"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                                <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                              </svg>
                            </button>
                          </div>
                        )}
                      </div>

                      <div className="mt-4">
                        <p className="text-sm font-medium text-gray-500">Customer Information</p>
                        <div className="mt-1 grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Name</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.firstName} {selectedPayment.customerInfo.lastName}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Email</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.email}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Phone</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.phone}</p>
                          </div>
                          {selectedPayment.customerInfo.address && (
                            <div>
                              <p className="text-sm text-gray-500">Address</p>
                              <p className="text-sm text-gray-900">{selectedPayment.customerInfo.address}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {selectedPayment.adminNotes && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-500">Admin Notes</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.adminNotes}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeModal}
                >
                  Close
                </button>
                {!editLicenseKey && (
                  <button
                    type="button"
                    onClick={() => handleResendLicense(selectedPayment.orderId)}
                    disabled={resendLoading}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:mr-3 sm:w-auto sm:text-sm disabled:opacity-50"
                  >
                    {resendLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Sending...
                      </>
                    ) : (
                      'Resend License Key'
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default ApprovedPaymentsPage;
