const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
require('dotenv').config();

// Import database configuration
const { testConnection } = require('./config/db');

// Import routes
const paymentRoutes = require('./routes/payment');
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');
const affiliateRoutes = require('./routes/affiliate');
const withdrawalRoutes = require('./routes/withdrawal');
const websiteContentRoutes = require('./routes/websiteContent');

// Initialize express app
const app = express();

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000', 'https://getmetamaster.com', 'https://www.getmetamaster.com'],
  credentials: true
}));
app.use(express.json());
app.use(morgan('dev'));

// Add request logging middleware
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Test MySQL connection
testConnection()
  .then(connected => {
    if (!connected) {
      console.error('Failed to connect to MySQL. Please check your configuration.');
      process.exit(1);
    }
  })
  .catch(err => {
    console.error('MySQL connection error:', err);
    process.exit(1);
  });

// Import tutorial routes
const tutorialRoutes = require('./routes/tutorial');
const tutorialCategoryRoutes = require('./routes/tutorialCategory');

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/users', userRoutes);
app.use('/api/affiliate', affiliateRoutes);
app.use('/api/withdrawal', withdrawalRoutes);
app.use('/api/website-content', websiteContentRoutes);
app.use('/api/tutorials', tutorialRoutes);
app.use('/api/tutorial-categories', tutorialCategoryRoutes);
app.use('/api/admin/affiliate', require('./routes/admin-affiliate'));

// Basic route for testing
app.get('/', (req, res) => {
  res.send('Meta Master API is running');
});


// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    success: false,
    message: 'Server error',
    error: process.env.NODE_ENV === 'development' ? err.message : {}
  });
});

// Start server
const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
