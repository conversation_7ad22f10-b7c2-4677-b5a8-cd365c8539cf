-- Users table
CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  email VARCHAR(100) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  affiliate_id VARCHAR(50) UNIQUE,
  affiliate_commission DECIMAL(10, 2) DEFAULT 0,
  withdrawn_commission DECIMAL(10, 2) DEFAULT 0,
  referred_by INT DEFAULT NULL,
  payment_info_bkash_number VARCHAR(20) DEFAULT NULL,
  payment_info_account_type ENUM('personal', 'agent', 'merchant') DEFAULT 'personal',
  reset_password_token VARCHAR(255) DEFAULT NULL,
  reset_password_expires DATETIME DEFAULT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Admins table
CREATE TABLE IF NOT EXISTS admins (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('admin', 'superadmin') DEFAULT 'admin',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  order_id VARCHAR(50) NOT NULL UNIQUE,
  transaction_id VARCHAR(100) NOT NULL,
  payment_method ENUM('bkash', 'nagad', 'rocket', 'upay') NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  plan ENUM('Monthly', 'Yearly', 'Lifetime') NOT NULL,
  user_id INT,
  referred_by INT DEFAULT NULL,
  is_affiliate_commission_paid BOOLEAN DEFAULT FALSE,
  status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  license_key VARCHAR(255) DEFAULT NULL,
  license_key_sent BOOLEAN DEFAULT FALSE,
  admin_notes TEXT,
  approved_by INT DEFAULT NULL,
  approved_at DATETIME DEFAULT NULL,
  -- Customer information fields
  customer_name VARCHAR(200) DEFAULT NULL,
  customer_email VARCHAR(100) DEFAULT NULL,
  customer_phone VARCHAR(20) DEFAULT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (referred_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (approved_by) REFERENCES admins(id) ON DELETE SET NULL
);

-- Affiliate transactions table
CREATE TABLE IF NOT EXISTS affiliate_transactions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  referrer_id INT NOT NULL,
  referred_user_id INT NOT NULL,
  payment_id INT NOT NULL,
  original_amount DECIMAL(10, 2) NOT NULL,
  commission_amount DECIMAL(10, 2) NOT NULL,
  commission_rate DECIMAL(4, 2) NOT NULL DEFAULT 0.2,
  status ENUM('pending', 'approved', 'rejected', 'paid') DEFAULT 'pending',
  plan ENUM('Monthly', 'Yearly', 'Lifetime') NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (referrer_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (referred_user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
);

-- Withdrawals table
CREATE TABLE IF NOT EXISTS withdrawals (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  amount DECIMAL(10, 2) NOT NULL,
  payment_method ENUM('bkash') DEFAULT 'bkash',
  bkash_number VARCHAR(20) NOT NULL,
  account_type ENUM('personal', 'agent', 'merchant') DEFAULT 'personal',
  status ENUM('pending', 'approved', 'rejected', 'paid') DEFAULT 'pending',
  admin_notes TEXT,
  transaction_id VARCHAR(100),
  processed_by INT DEFAULT NULL,
  processed_at DATETIME DEFAULT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (processed_by) REFERENCES admins(id) ON DELETE SET NULL
);

-- License keys table
CREATE TABLE IF NOT EXISTS license_keys (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  payment_id INT NOT NULL,
  license_key VARCHAR(255) DEFAULT '',
  plan ENUM('Monthly', 'Yearly', 'Lifetime') NOT NULL,
  purchase_date DATETIME DEFAULT CURRENT_TIMESTAMP,
  expiry_date DATETIME DEFAULT NULL,
  status ENUM('active', 'expired', 'revoked', 'pending') DEFAULT 'pending',
  payment_status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE
);

-- Website content tables
CREATE TABLE IF NOT EXISTS website_content (
  id INT AUTO_INCREMENT PRIMARY KEY,
  version INT DEFAULT 1,
  is_published BOOLEAN DEFAULT FALSE,
  published_at DATETIME DEFAULT NULL,
  updated_by INT DEFAULT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (updated_by) REFERENCES admins(id) ON DELETE SET NULL
);

CREATE TABLE IF NOT EXISTS content_sections (
  id INT AUTO_INCREMENT PRIMARY KEY,
  content_id INT NOT NULL,
  section_id VARCHAR(50) NOT NULL,
  section_name VARCHAR(100) NOT NULL,
  title VARCHAR(255),
  subtitle TEXT,
  content TEXT,
  button_text VARCHAR(50),
  button_url VARCHAR(255),
  image_url VARCHAR(255),
  order_num INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  FOREIGN KEY (content_id) REFERENCES website_content(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS features (
  id INT AUTO_INCREMENT PRIMARY KEY,
  content_id INT NOT NULL,
  title VARCHAR(100) NOT NULL,
  description TEXT,
  icon VARCHAR(50),
  order_num INT DEFAULT 0,
  FOREIGN KEY (content_id) REFERENCES website_content(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS faqs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  content_id INT NOT NULL,
  question VARCHAR(255) NOT NULL,
  answer TEXT NOT NULL,
  order_num INT DEFAULT 0,
  FOREIGN KEY (content_id) REFERENCES website_content(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS testimonials (
  id INT AUTO_INCREMENT PRIMARY KEY,
  content_id INT NOT NULL,
  name VARCHAR(100) NOT NULL,
  role VARCHAR(100),
  content TEXT NOT NULL,
  avatar_url VARCHAR(255),
  order_num INT DEFAULT 0,
  FOREIGN KEY (content_id) REFERENCES website_content(id) ON DELETE CASCADE
);

-- Tutorial tables
CREATE TABLE IF NOT EXISTS tutorial_categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  order_num INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS tutorials (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  duration VARCHAR(20),
  level VARCHAR(50),
  video_url VARCHAR(255),
  thumbnail_url VARCHAR(255),
  category_id INT,
  order_num INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_by INT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (category_id) REFERENCES tutorial_categories(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES admins(id) ON DELETE SET NULL
);
