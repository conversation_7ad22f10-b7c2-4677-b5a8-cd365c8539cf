import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import { EyeIcon, CheckIcon, XMarkIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';

const AllPaymentsPage = () => {
  const [payments, setPayments] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedPayment, setSelectedPayment] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [actionType, setActionType] = useState(null);
  const [adminNotes, setAdminNotes] = useState('');
  const [licenseKey, setLicenseKey] = useState('');
  const [filter, setFilter] = useState('all');

  useEffect(() => {
    fetchPayments();
  }, []);

  const fetchPayments = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payments`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch payments');
      }

      const data = await response.json();
      setPayments(data.data);
    } catch (err) {
      console.error('Error fetching payments:', err);
      setError('Failed to load payments. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewDetails = (payment) => {
    setSelectedPayment(payment);
    setIsModalOpen(true);
    setActionType(null);
    setAdminNotes('');
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
    setActionType(null);
    setAdminNotes('');
    setLicenseKey('');
  };

  const handleAction = (type) => {
    setActionType(type);

    // Clear any previous license key and focus on the input field when approving
    if (type === 'approve') {
      setLicenseKey(''); // Start with an empty license key field

      // Set a small timeout to ensure the modal is open before focusing
      setTimeout(() => {
        const licenseKeyInput = document.getElementById('licenseKey');
        if (licenseKeyInput) {
          licenseKeyInput.focus();
        }
      }, 100);
    }
  };

  const confirmAction = async () => {
    if (!selectedPayment || !actionType) return;

    // Validate that a license key is provided when approving
    if (actionType === 'approve' && (!licenseKey || licenseKey.trim() === '')) {
      alert('Please enter a license key before approving the payment. This key will be shown in the user\'s dashboard.');

      // Focus on the license key input field
      document.getElementById('licenseKey').focus();
      return;
    }

    setActionLoading(true);

    try {
      const token = localStorage.getItem('adminToken');
      const endpoint = actionType === 'approve' ? 'approve' :
                      actionType === 'reject' ? 'reject' :
                      actionType === 'resend' ? 'resend-license' : '';

      if (!endpoint) {
        throw new Error('Invalid action type');
      }

      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/payments/${selectedPayment.orderId}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: actionType === 'resend' ? null :
              actionType === 'approve' ? JSON.stringify({ adminNotes, licenseKey }) :
              JSON.stringify({ adminNotes })
      });

      if (!response.ok) {
        throw new Error(`Failed to ${actionType} payment`);
      }

      // Refresh the payments list
      await fetchPayments();

      // Close the modal
      closeModal();

      // Show success message with license key information if approved
      if (actionType === 'approve') {
        alert(`Payment approved successfully!\n\nThe license key you entered (${licenseKey}) has been assigned to the user and is now visible in their dashboard.`);
      } else {
        alert(`Payment ${actionType === 'reject' ? 'rejected' : 'license key resent'} successfully`);
      }
    } catch (err) {
      console.error(`Error ${actionType}ing payment:`, err);
      alert(`Failed to ${actionType} payment. Please try again.`);
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case 'pending':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Pending</span>;
      case 'approved':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Approved</span>;
      case 'rejected':
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Rejected</span>;
      default:
        return <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">{status}</span>;
    }
  };

  const filteredPayments = filter === 'all'
    ? payments
    : payments.filter(payment => payment.verificationStatus === filter);

  return (
    <AdminLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold text-gray-900">All Payments</h1>
        <p className="mt-1 text-sm text-gray-500">
          View and manage all payment transactions
        </p>

        <div className="mt-4 flex space-x-2">
          <button
            onClick={() => setFilter('all')}
            className={`px-3 py-1 rounded-md text-sm font-medium ${
              filter === 'all' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 border border-gray-300'
            }`}
          >
            All
          </button>
          <button
            onClick={() => setFilter('pending')}
            className={`px-3 py-1 rounded-md text-sm font-medium ${
              filter === 'pending' ? 'bg-yellow-600 text-white' : 'bg-white text-gray-700 border border-gray-300'
            }`}
          >
            Pending
          </button>
          <button
            onClick={() => setFilter('approved')}
            className={`px-3 py-1 rounded-md text-sm font-medium ${
              filter === 'approved' ? 'bg-green-600 text-white' : 'bg-white text-gray-700 border border-gray-300'
            }`}
          >
            Approved
          </button>
          <button
            onClick={() => setFilter('rejected')}
            className={`px-3 py-1 rounded-md text-sm font-medium ${
              filter === 'rejected' ? 'bg-red-600 text-white' : 'bg-white text-gray-700 border border-gray-300'
            }`}
          >
            Rejected
          </button>
        </div>

        {isLoading ? (
          <div className="flex justify-center mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="mt-8 bg-red-50 border-l-4 border-red-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        ) : filteredPayments.length === 0 ? (
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
            <p className="text-gray-500">No payments found</p>
          </div>
        ) : (
          <div className="mt-4 bg-white shadow overflow-hidden sm:rounded-md">
            <ul className="divide-y divide-gray-200">
              {filteredPayments.map((payment) => (
                <li key={payment.orderId}>
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-blue-600 truncate">
                          {payment.orderId}
                        </p>
                        <div className="ml-2">
                          {getStatusBadge(payment.verificationStatus)}
                        </div>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <button
                          onClick={() => handleViewDetails(payment)}
                          className="mr-2 px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <EyeIcon className="h-4 w-4 inline mr-1" />
                          Details
                        </button>

                        {payment.verificationStatus === 'pending' && (
                          <>
                            <button
                              onClick={() => {
                                setSelectedPayment(payment);
                                handleAction('approve');
                                setIsModalOpen(true);
                              }}
                              className="mr-2 px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700"
                            >
                              <CheckIcon className="h-4 w-4 inline mr-1" />
                              Approve
                            </button>
                            <button
                              onClick={() => {
                                setSelectedPayment(payment);
                                handleAction('reject');
                                setIsModalOpen(true);
                              }}
                              className="px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700"
                            >
                              <XMarkIcon className="h-4 w-4 inline mr-1" />
                              Reject
                            </button>
                          </>
                        )}

                        {payment.verificationStatus === 'approved' && (
                          <button
                            onClick={() => {
                              setSelectedPayment(payment);
                              handleAction('resend');
                              setIsModalOpen(true);
                            }}
                            className="px-3 py-1 border border-transparent rounded-md text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                          >
                            <PaperAirplaneIcon className="h-4 w-4 inline mr-1" />
                            Resend License
                          </button>
                        )}
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          {payment.customerInfo.firstName} {payment.customerInfo.lastName}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          {payment.paymentMethod.toUpperCase()} - ৳{payment.amount}
                        </p>
                        <p className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0 sm:ml-6">
                          {payment.plan}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>
                          {formatDate(payment.createdAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Modal for payment details and actions */}
      {isModalOpen && selectedPayment && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {actionType === 'approve' ? 'Approve Payment' :
                       actionType === 'reject' ? 'Reject Payment' :
                       actionType === 'resend' ? 'Resend License Key' :
                       'Payment Details'}
                    </h3>

                    {/* License Key Input - Only visible when approving */}
                    {actionType === 'approve' && (
                      <div className="mt-4 bg-green-50 border border-green-200 rounded-lg p-4">
                        <h4 className="text-md font-semibold text-green-800 mb-2 flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                          Enter License Key
                        </h4>
                        <p className="text-sm text-green-700 mb-3">
                          <strong>Important:</strong> Please enter a license key manually. This key will be shown in the user's dashboard after approval.
                        </p>
                        <div className="flex items-center">
                          <input
                            type="text"
                            id="licenseKey"
                            name="licenseKey"
                            className="block w-full border border-green-300 rounded-md shadow-sm py-3 px-4 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm font-mono bg-white"
                            placeholder="Enter license key for this user (required)"
                            value={licenseKey}
                            onChange={(e) => setLicenseKey(e.target.value)}
                            required
                          />
                        </div>
                        <p className="mt-2 text-xs text-green-600">
                          Suggested format: XXXXX-XXXXX-XXXXX-XXXXX-XXXXX
                        </p>
                      </div>
                    )}

                    <div className="mt-4 border-t border-gray-200 pt-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Order ID</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.orderId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Transaction ID</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.transactionId}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Payment Method</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.paymentMethod.toUpperCase()}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Amount</p>
                          <p className="mt-1 text-sm text-gray-900">৳{selectedPayment.amount}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Plan</p>
                          <p className="mt-1 text-sm text-gray-900">{selectedPayment.plan}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Date</p>
                          <p className="mt-1 text-sm text-gray-900">{formatDate(selectedPayment.createdAt)}</p>
                        </div>
                      </div>

                      {selectedPayment.licenseKey && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-500">License Key</p>
                          <p className="mt-1 text-sm font-mono bg-gray-100 p-2 rounded">{selectedPayment.licenseKey}</p>
                        </div>
                      )}

                      <div className="mt-4">
                        <p className="text-sm font-medium text-gray-500">Customer Information</p>
                        <div className="mt-1 grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-500">Name</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.firstName} {selectedPayment.customerInfo.lastName}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Email</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.email}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Phone</p>
                            <p className="text-sm text-gray-900">{selectedPayment.customerInfo.phone}</p>
                          </div>
                          {selectedPayment.customerInfo.address && (
                            <div>
                              <p className="text-sm text-gray-500">Address</p>
                              <p className="text-sm text-gray-900">{selectedPayment.customerInfo.address}</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {selectedPayment.adminNotes && (
                        <div className="mt-4">
                          <p className="text-sm font-medium text-gray-500">Admin Notes</p>
                          <p className="mt-1 text-sm text-gray-900 p-2 rounded border-l-4 border-gray-300">{selectedPayment.adminNotes}</p>
                        </div>
                      )}

                      {(actionType === 'approve' || actionType === 'reject') && (
                        <div className="mt-4">
                          <label htmlFor="adminNotes" className="block text-sm font-medium text-gray-700">
                            {actionType === 'approve' ? 'Admin Notes (Optional)' : 'Rejection Reason'}
                          </label>
                          <textarea
                            id="adminNotes"
                            name="adminNotes"
                            rows="3"
                            className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                            placeholder={actionType === 'approve' ? 'Optional notes about this approval' : 'Reason for rejection'}
                            value={adminNotes}
                            onChange={(e) => setAdminNotes(e.target.value)}
                            required={actionType === 'reject'}
                          ></textarea>
                        </div>
                      )}

                      {actionType === 'resend' && (
                        <div className="mt-4 bg-yellow-50 p-4 rounded-md">
                          <div className="flex">
                            <div className="flex-shrink-0">
                              <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div className="ml-3">
                              <h3 className="text-sm font-medium text-yellow-800">Resend License Key</h3>
                              <div className="mt-2 text-sm text-yellow-700">
                                <p>
                                  This will send the license key to the customer's email address: <strong>{selectedPayment.customerInfo.email}</strong>
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                {actionType && (
                  <button
                    type="button"
                    onClick={confirmAction}
                    disabled={actionLoading || (actionType === 'reject' && !adminNotes)}
                    className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white sm:ml-3 sm:w-auto sm:text-sm ${
                      actionType === 'approve' ? 'bg-green-600 hover:bg-green-700' :
                      actionType === 'reject' ? 'bg-red-600 hover:bg-red-700' :
                      'bg-blue-600 hover:bg-blue-700'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      actionType === 'approve' ? 'focus:ring-green-500' :
                      actionType === 'reject' ? 'focus:ring-red-500' :
                      'focus:ring-blue-500'
                    } disabled:opacity-50`}
                  >
                    {actionLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      actionType === 'approve' ? 'Approve' :
                      actionType === 'reject' ? 'Reject' :
                      'Resend License Key'
                    )}
                  </button>
                )}
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeModal}
                >
                  {actionType ? 'Cancel' : 'Close'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default AllPaymentsPage;
