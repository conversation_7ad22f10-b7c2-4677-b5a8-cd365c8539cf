const mysql = require('mysql2/promise');
require('dotenv').config();

// Create a connection pool
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'Jayed123#@!',
  database: process.env.DB_NAME || 'meta_master',
  port: process.env.DB_PORT || 3306,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  authPlugins: {
    mysql_native_password: () => ({ type: 'mysql_native_password' })
  }
});

// Test the connection
const testConnection = async () => {
  try {
    // First try connecting without specifying a database
    const tempPool = mysql.createPool({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'Jayed123#@!',
      port: process.env.DB_PORT || 3306,
      waitForConnections: true,
      connectionLimit: 1,
      queueLimit: 0,
      authPlugins: {
        mysql_native_password: () => ({ type: 'mysql_native_password' })
      }
    });

    console.log('Attempting to connect to MySQL server...');
    const tempConnection = await tempPool.getConnection();
    console.log('Successfully connected to MySQL server');

    // Check if the database exists
    const [rows] = await tempConnection.query(`SHOW DATABASES LIKE '${process.env.DB_NAME}'`);
    if (rows.length === 0) {
      console.log(`Database '${process.env.DB_NAME}' does not exist. Creating it...`);
      await tempConnection.query(`CREATE DATABASE IF NOT EXISTS ${process.env.DB_NAME}`);
      console.log(`Database '${process.env.DB_NAME}' created successfully`);
    } else {
      console.log(`Database '${process.env.DB_NAME}' exists`);
    }

    // Now try connecting to the specific database
    tempConnection.release();
    await tempPool.end();

    const connection = await pool.getConnection();
    console.log('MySQL connected successfully with database');
    connection.release();
    return true;
  } catch (error) {
    console.error('MySQL connection error:', error);
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);
    console.error('Error SQL state:', error.sqlState);

    // Provide more specific error messages based on error code
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('Access denied. Please check your MySQL username and password.');
      console.error('Current connection details:');
      console.error(`Host: ${process.env.DB_HOST || 'localhost'}`);
      console.error(`User: ${process.env.DB_USER || 'root'}`);
      console.error(`Password: ${process.env.DB_PASSWORD ? '[REDACTED]' : 'none'}`);
      console.error(`Port: ${process.env.DB_PORT || 3306}`);
    } else if (error.code === 'ECONNREFUSED') {
      console.error('Connection refused. Please check if MySQL server is running and the host/port are correct.');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error(`Database '${process.env.DB_NAME}' does not exist.`);
    }

    return false;
  }
};

// Helper function to execute queries
const query = async (sql, params) => {
  try {
    return await pool.execute(sql, params);
  } catch (error) {
    console.error('MySQL query error:', error);
    throw error;
  }
};

module.exports = {
  pool,
  testConnection,
  query
};
