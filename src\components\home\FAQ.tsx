import { motion, AnimatePresence } from 'framer-motion'
import { Disclosure } from '@headlessui/react'
import { ChevronUpIcon } from '@heroicons/react/24/solid'
import SectionHeader from '../ui/SectionHeader'

const faqs = [
  {
    question: "What is Meta Master?",
    answer: "Meta Master is an AI-powered metadata generator designed specifically for microstock contributors. It automatically generates titles, keywords, and descriptions for images, vectors, and videos to help your content get discovered on platforms like Adobe Stock, Shutterstock, Freepik, and more."
  },
  {
    question: "Do I need any special hardware to run Meta Master?",
    answer: "Meta Master runs on standard Windows 10/11 computers. It doesn't require any special hardware as the AI processing happens in the cloud using Google's Gemini API. The software itself is lightweight and designed to start quickly, typically within 2-5 seconds."
  },
  {
    question: "How does the licensing work?",
    answer: "Meta Master licenses are valid for one year and include all updates released during that period. Each license is tied to a specific number of devices depending on your plan (1 device for Standard, 2 for Professional, 5 for Agency). After the year, you can renew your license to continue receiving updates and support."
  },
  {
    question: "Can I process files in batch?",
    answer: "Yes, all plans include batch processing capabilities. You can select multiple files or entire folders to process at once. The Professional and Agency plans offer unlimited batch processing with pause, resume, and retry functionality for maximum efficiency."
  },
  {
    question: "What file formats are supported?",
    answer: "Meta Master supports a wide range of formats including images (JPG, PNG), vector files (SVG, EPS, AI), and videos (MP4, MOV, AVI, MKV). The software automatically detects the file type and applies the appropriate processing method."
  },
  {
    question: "Do you offer a free trial?",
    answer: "Yes, we offer a 7-day free trial that gives you access to all features of the Professional plan. This allows you to test Meta Master with your own content before making a purchase decision. No credit card is required for the trial."
  },
  {
    question: "What kind of support is provided?",
    answer: "All plans include email support. Professional plan users receive priority email support with faster response times. Agency plan users get access to phone support and a dedicated account manager. We also provide comprehensive documentation and video tutorials to help you get the most out of Meta Master."
  },
  {
    question: "Do I need a Google Gemini API key?",
    answer: "Yes, Meta Master uses Google's Gemini AI for metadata generation, so you'll need to provide your own API key. Google offers a free tier that's sufficient for most individual users. We provide step-by-step instructions on how to obtain and set up your API key."
  }
]

export default function FAQ() {
  return (
    <section id="faq" className="section">
      <div className="container">
        <SectionHeader
          title="Frequently Asked Questions"
          subtitle="Everything you need to know about Meta Master"
        />
        
        <div className="max-w-3xl mx-auto">
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Disclosure>
                  {({ open }) => (
                    <>
                      <Disclosure.Button className="flex w-full justify-between rounded-lg bg-dark-800 px-4 py-4 text-left text-lg font-medium text-white hover:bg-dark-700 focus:outline-none focus-visible:ring focus-visible:ring-primary">
                        <span>{faq.question}</span>
                        <ChevronUpIcon
                          className={`${
                            open ? 'rotate-180 transform' : ''
                          } h-5 w-5 text-primary`}
                        />
                      </Disclosure.Button>
                      <AnimatePresence>
                        {open && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                          >
                            <Disclosure.Panel className="px-4 pt-4 pb-2 text-dark-300">
                              {faq.answer}
                            </Disclosure.Panel>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </>
                  )}
                </Disclosure>
              </motion.div>
            ))}
          </div>
          
          <motion.div 
            className="mt-12 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <p className="text-dark-400 mb-4">Still have questions?</p>
            <a href="/contact" className="btn btn-outline">
              Contact Us
            </a>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
