import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import { EyeIcon, CheckIcon, XMarkIcon, UserGroupIcon } from '@heroicons/react/24/outline';

const AffiliateLeaderboardPage = () => {
  const [affiliates, setAffiliates] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedUser, setSelectedUser] = useState(null);
  const [userTransactions, setUserTransactions] = useState([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isTransactionsLoading, setIsTransactionsLoading] = useState(false);
  const [transactionsError, setTransactionsError] = useState(null);
  const [selectedTransaction, setSelectedTransaction] = useState(null);
  const [actionType, setActionType] = useState(null); // 'approve' or 'reject'
  const [adminNotes, setAdminNotes] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchAffiliates();
  }, []);

  const fetchAffiliates = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';

      console.log('Fetching affiliate leaderboard data...');

      // Add a timestamp to prevent caching
      const timestamp = new Date().getTime();

      const response = await fetch(`${apiUrl}/api/admin/affiliate/leaderboard?t=${timestamp}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch affiliate data');
      }

      const data = await response.json();
      console.log(`Received ${data.data ? data.data.length : 0} affiliates from server`);

      if (data.data && Array.isArray(data.data)) {
        // Make sure we're working with numbers for all numeric fields
        const processedData = data.data.map(affiliate => ({
          ...affiliate,
          totalEarnings: parseFloat(affiliate.totalEarnings) || 0,
          withdrawnAmount: parseFloat(affiliate.withdrawnAmount) || 0,
          availableBalance: parseFloat(affiliate.availableBalance) || 0,
          totalReferrals: parseInt(affiliate.totalReferrals) || 0,
          pendingReferrals: parseInt(affiliate.pendingReferrals) || 0
        }));

        setAffiliates(processedData);
      } else {
        console.error('Invalid data format received:', data);
        setAffiliates([]);
      }
    } catch (err) {
      console.error('Error fetching affiliate data:', err);
      setError('Failed to load affiliate data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleViewTransactions = async (user) => {
    setSelectedUser(user);
    setIsModalOpen(true);
    setIsTransactionsLoading(true);
    setTransactionsError(null);

    try {
      const token = localStorage.getItem('adminToken');
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';

      const response = await fetch(`${apiUrl}/api/affiliate/admin/transactions/${user._id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user transactions');
      }

      const data = await response.json();
      setUserTransactions(data.data);
    } catch (err) {
      console.error('Error fetching user transactions:', err);
      setTransactionsError('Failed to load user transactions. Please try again.');
    } finally {
      setIsTransactionsLoading(false);
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
    setUserTransactions([]);
    setSelectedTransaction(null);
    setActionType(null);
    setAdminNotes('');
  };

  const handleAction = (transaction, type) => {
    setSelectedTransaction(transaction);
    setActionType(type);
  };

  const confirmAction = async () => {
    if (!selectedTransaction || !actionType) return;

    setActionLoading(true);

    try {
      const token = localStorage.getItem('adminToken');
      const endpoint = actionType === 'approve' ? 'approve' : 'reject';
      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';

      // Get transaction ID, handling both MongoDB and MySQL formats
      const transactionId = selectedTransaction._id || selectedTransaction.id;
      console.log(`Sending ${actionType} request for transaction ID: ${transactionId}`);

      const response = await fetch(`${apiUrl}/api/affiliate/admin/transaction/${transactionId}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({
          adminNotes
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to ${actionType} transaction`);
      }

      // Get the response data
      const result = await response.json();
      console.log(`${actionType} response:`, result);

      // Reset action state first
      setSelectedTransaction(null);
      setActionType(null);
      setAdminNotes('');

      // Show success message
      alert(`Transaction ${actionType}d successfully`);

      // Force a direct database update check
      try {
        const fixResponse = await fetch(`${apiUrl}/api/affiliate/fix-balance`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (fixResponse.ok) {
          console.log('Balance fix completed');
        }
      } catch (fixError) {
        console.error('Error fixing balance:', fixError);
      }

      // Refresh the transactions list
      if (selectedUser) {
        console.log(`Refreshing transactions for user: ${selectedUser.name}`);
        await handleViewTransactions(selectedUser);
      }

      // Refresh the affiliates list with a longer delay to ensure backend updates are complete
      console.log('Refreshing affiliate leaderboard data');
      setTimeout(async () => {
        await fetchAffiliates();
      }, 1000);

      // Schedule another refresh after a longer delay as a backup
      setTimeout(async () => {
        console.log('Performing secondary refresh of affiliate data');
        await fetchAffiliates();
      }, 3000);

    } catch (err) {
      console.error(`Error ${actionType}ing transaction:`, err);
      alert(`Failed to ${actionType} transaction. Please try again.`);
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <AdminLayout>
      <div className="py-6">
        <h1 className="text-2xl font-semibold text-gray-900">Affiliate Leaderboard</h1>
        <p className="mt-1 text-sm text-gray-500">
          View and manage affiliate performance and referrals
        </p>

        {isLoading ? (
          <div className="flex justify-center mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="mt-8 bg-red-50 border-l-4 border-red-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        ) : affiliates.length === 0 ? (
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
            <p className="text-gray-500">No affiliate data found</p>
          </div>
        ) : (
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Affiliate
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Referrals
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pending
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Total Earnings
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Withdrawn
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Available Balance
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {affiliates.map((affiliate) => (
                  <tr key={affiliate._id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10 bg-gray-200 rounded-full flex items-center justify-center">
                          <UserGroupIcon className="h-6 w-6 text-gray-500" />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{affiliate.name}</div>
                          <div className="text-sm text-gray-500">{affiliate.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {affiliate.totalReferrals}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">
                        {affiliate.pendingReferrals}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ৳{affiliate.totalEarnings.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ৳{affiliate.withdrawnAmount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      ৳{affiliate.availableBalance.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleViewTransactions(affiliate)}
                        className="text-blue-600 hover:text-blue-900 bg-blue-100 hover:bg-blue-200 px-3 py-1 rounded-md"
                      >
                        <EyeIcon className="h-4 w-4 inline mr-1" />
                        View Referrals
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Modal for user transactions */}
      {isModalOpen && selectedUser && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 transition-opacity" aria-hidden="true">
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                      {actionType === 'approve' ? 'Approve Referral' :
                       actionType === 'reject' ? 'Reject Referral' :
                       `Referrals for ${selectedUser.name}`}
                    </h3>

                    {selectedTransaction && actionType && (
                      <div className="mt-4">
                        <label htmlFor="adminNotes" className="block text-sm font-medium text-gray-700">
                          Admin Notes
                        </label>
                        <textarea
                          id="adminNotes"
                          name="adminNotes"
                          rows="3"
                          className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                          placeholder={actionType === 'approve' ? 'Optional notes about this approval' : 'Reason for rejection'}
                          value={adminNotes}
                          onChange={(e) => setAdminNotes(e.target.value)}
                        ></textarea>
                      </div>
                    )}

                    {!selectedTransaction && (
                      <div className="mt-4 bg-blue-50 p-4 rounded-md">
                        <div className="flex">
                          <div className="flex-shrink-0">
                            <UserGroupIcon className="h-5 w-5 text-blue-400" />
                          </div>
                          <div className="ml-3">
                            <h3 className="text-sm font-medium text-blue-800">Affiliate Summary</h3>
                            <div className="mt-2 text-sm text-blue-700">
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <p className="font-semibold">Total Referrals:</p>
                                  <p>{selectedUser.totalReferrals}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Total Earnings:</p>
                                  <p>৳{selectedUser.totalEarnings.toLocaleString()}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Withdrawn Amount:</p>
                                  <p>৳{selectedUser.withdrawnAmount.toLocaleString()}</p>
                                </div>
                                <div>
                                  <p className="font-semibold">Available Balance:</p>
                                  <p>৳{selectedUser.availableBalance.toLocaleString()}</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {isTransactionsLoading ? (
                      <div className="flex justify-center mt-8">
                        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                      </div>
                    ) : transactionsError ? (
                      <div className="mt-4 bg-red-50 border-l-4 border-red-500 p-4">
                        <div className="flex">
                          <div className="ml-3">
                            <p className="text-sm text-red-700">{transactionsError}</p>
                          </div>
                        </div>
                      </div>
                    ) : userTransactions.length === 0 ? (
                      <div className="mt-4 text-center py-4">
                        <p className="text-gray-500">No referrals found for this user</p>
                      </div>
                    ) : !selectedTransaction && (
                      <div className="mt-4 overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                          <thead className="bg-gray-50">
                            <tr>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Date
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Referred User
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Plan
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Amount
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Commission
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Status
                              </th>
                              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {userTransactions.map((transaction) => (
                              <tr key={transaction._id} className="hover:bg-gray-50">
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {formatDate(transaction.createdAt)}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm font-medium text-gray-900">
                                    {transaction.referredUser ?
                                      `${transaction.referredUser.firstName} ${transaction.referredUser.lastName}` :
                                      'Unknown User'}
                                  </div>
                                  <div className="text-sm text-gray-500">
                                    {transaction.referredUser?.email}
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {transaction.plan}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  ৳{transaction.originalAmount.toLocaleString()}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  ৳{transaction.commissionAmount.toLocaleString()}
                                  <span className="text-xs text-gray-400 ml-1">
                                    ({transaction.commissionRate * 100}%)
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                    ${transaction.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                      transaction.status === 'approved' ? 'bg-green-100 text-green-800' :
                                      transaction.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                      'bg-blue-100 text-blue-800'}`}>
                                    {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                                  </span>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                  {transaction.status === 'pending' && (
                                    <div className="flex space-x-2">
                                      <button
                                        onClick={() => handleAction(transaction, 'approve')}
                                        className="text-green-600 hover:text-green-900 bg-green-100 hover:bg-green-200 px-2 py-1 rounded-md"
                                      >
                                        <CheckIcon className="h-4 w-4 inline" />
                                      </button>
                                      <button
                                        onClick={() => handleAction(transaction, 'reject')}
                                        className="text-red-600 hover:text-red-900 bg-red-100 hover:bg-red-200 px-2 py-1 rounded-md"
                                      >
                                        <XMarkIcon className="h-4 w-4 inline" />
                                      </button>
                                    </div>
                                  )}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                {selectedTransaction && actionType ? (
                  <button
                    type="button"
                    onClick={confirmAction}
                    disabled={actionLoading}
                    className={`w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 text-base font-medium text-white sm:ml-3 sm:w-auto sm:text-sm ${
                      actionType === 'approve' ? 'bg-green-600 hover:bg-green-700' : 'bg-red-600 hover:bg-red-700'
                    } focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      actionType === 'approve' ? 'focus:ring-green-500' : 'focus:ring-red-500'
                    } disabled:opacity-50`}
                  >
                    {actionLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      actionType === 'approve' ? 'Approve' : 'Reject'
                    )}
                  </button>
                ) : null}
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => {
                    if (selectedTransaction) {
                      setSelectedTransaction(null);
                      setActionType(null);
                      setAdminNotes('');
                    } else {
                      closeModal();
                    }
                  }}
                >
                  {selectedTransaction ? 'Back' : 'Close'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
};

export default AffiliateLeaderboardPage;
