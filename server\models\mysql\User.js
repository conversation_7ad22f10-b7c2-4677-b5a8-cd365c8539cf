const { query } = require('../../config/db');
const bcrypt = require('bcryptjs');

class User {
  // Helper method to format user data
  static _formatUser(user) {
    // Check if payment info fields exist in the database
    let hasPaymentInfoFields = false;
    try {
      hasPaymentInfoFields = user.hasOwnProperty('payment_info_bkash_number') ||
                            user.hasOwnProperty('payment_info_account_type');
    } catch (e) {
      console.error('Error checking payment info fields:', e);
    }

    // Get affiliate commission and withdrawn values
    const affiliateCommission = parseFloat(user.affiliate_commission || 0);
    const withdrawnCommission = parseFloat(user.withdrawn_commission || 0);
    const pendingWithdrawals = parseFloat(user.pending_withdrawals || 0);

    // Calculate available balance
    const availableBalance = affiliateCommission - withdrawnCommission - pendingWithdrawals;

    // Create formatted user object
    const formattedUser = {
      id: user.id,
      username: user.username,
      email: user.email,
      password: user.password,
      name: user.name,
      affiliateId: user.affiliate_id,
      affiliateCommission: affiliateCommission,
      withdrawnCommission: withdrawnCommission,
      pendingWithdrawals: pendingWithdrawals,
      availableBalance: availableBalance,
      referredBy: user.referred_by,
      paymentInfo: {
        bkashNumber: hasPaymentInfoFields ? user.payment_info_bkash_number || '' : '',
        accountType: hasPaymentInfoFields ? user.payment_info_account_type || 'personal' : 'personal'
      },
      resetPasswordToken: user.reset_password_token,
      resetPasswordExpires: user.reset_password_expires,
      createdAt: user.created_at,
      updatedAt: user.updated_at,
      // License keys will be added separately
      licenseKeys: []
    };

    return formattedUser;
  }
  // Find user by ID
  static async findById(id) {
    try {
      console.log('Finding user by ID:', id);

      // First check if payment info columns exist
      let paymentInfoColumns = '';
      try {
        const [columns] = await query('SHOW COLUMNS FROM users');
        const columnNames = columns.map(col => col.Field);

        if (columnNames.includes('payment_info_bkash_number')) {
          paymentInfoColumns += ', payment_info_bkash_number';
        }

        if (columnNames.includes('payment_info_account_type')) {
          paymentInfoColumns += ', payment_info_account_type';
        }

        console.log('Payment info columns:', paymentInfoColumns);
      } catch (columnError) {
        console.error('Error checking columns:', columnError);
      }

      // Check if pending_withdrawals column exists
      let pendingWithdrawalsColumn = '';
      try {
        const [pendingColumns] = await query('SHOW COLUMNS FROM users LIKE "pending_withdrawals"');
        if (pendingColumns.length > 0) {
          pendingWithdrawalsColumn = ', pending_withdrawals';
        }
      } catch (pendingError) {
        console.error('Error checking pending_withdrawals column:', pendingError);
      }

      const [rows] = await query(
        'SELECT id, username, email, name, affiliate_id, affiliate_commission, withdrawn_commission' +
        pendingWithdrawalsColumn + paymentInfoColumns + ', created_at, updated_at ' +
        'FROM users WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        console.log('No user found with ID:', id);
        return null;
      }

      // Convert snake_case to camelCase
      const user = rows[0];
      console.log('User found:', user.username);
      return this._formatUser(user);
    } catch (error) {
      console.error('Error finding user by ID:', error);
      throw error;
    }
  }

  // Find user by username or email
  static async findOne(criteria) {
    try {
      // Handle MongoDB-style $or operator for backward compatibility
      if (criteria.$or) {
        const orConditions = [];
        const params = [];

        criteria.$or.forEach(condition => {
          if (condition.username) {
            orConditions.push('username = ?');
            params.push(condition.username);
          }
          if (condition.email) {
            orConditions.push('email = ?');
            params.push(condition.email);
          }
          if (condition.affiliateId) {
            orConditions.push('affiliate_id = ?');
            params.push(condition.affiliateId);
          }
        });

        if (orConditions.length === 0) {
          throw new Error('Invalid search criteria');
        }

        const sql = `SELECT * FROM users WHERE ${orConditions.join(' OR ')}`;
        const [rows] = await query(sql, params);

        if (rows.length === 0) {
          return null;
        }

        // Convert snake_case to camelCase
        const user = rows[0];
        return this._formatUser(user);
      }

      // Handle direct criteria
      let sql = 'SELECT * FROM users WHERE ';
      const params = [];

      if (criteria.username) {
        sql += 'username = ?';
        params.push(criteria.username);
      } else if (criteria.email) {
        sql += 'email = ?';
        params.push(criteria.email);
      } else if (criteria.affiliateId) {
        sql += 'affiliate_id = ?';
        params.push(criteria.affiliateId);
      } else {
        throw new Error('Invalid search criteria');
      }

      const [rows] = await query(sql, params);

      if (rows.length === 0) {
        return null;
      }

      // Convert snake_case to camelCase
      const user = rows[0];
      return this._formatUser(user);
    } catch (error) {
      console.error('Error finding user:', error);
      throw error;
    }
  }

  // Create a new user
  static async create(userData) {
    try {
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // Generate affiliate ID from username
      const affiliateId = userData.username.replace(/[^a-zA-Z0-9]/g, '').toLowerCase();

      const [result] = await query(
        'INSERT INTO users (username, email, password, name, affiliate_id) VALUES (?, ?, ?, ?, ?)',
        [userData.username, userData.email, hashedPassword, userData.name, affiliateId]
      );

      // Get the newly created user
      return await this.findById(result.insertId);
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Update user
  static async update(id, updateData) {
    try {
      console.log('Updating user with ID:', id, 'Data:', JSON.stringify(updateData));

      // Check if payment info columns exist
      if (updateData.paymentInfoBkashNumber || updateData.paymentInfoAccountType) {
        try {
          // Check if payment_info_bkash_number column exists
          const [bkashColumns] = await query('SHOW COLUMNS FROM users LIKE "payment_info_bkash_number"');
          if (bkashColumns.length === 0) {
            console.log('Adding payment_info_bkash_number column to users table');
            await query('ALTER TABLE users ADD COLUMN payment_info_bkash_number VARCHAR(20)');
          }

          // Check if payment_info_account_type column exists
          const [accountTypeColumns] = await query('SHOW COLUMNS FROM users LIKE "payment_info_account_type"');
          if (accountTypeColumns.length === 0) {
            console.log('Adding payment_info_account_type column to users table');
            await query('ALTER TABLE users ADD COLUMN payment_info_account_type VARCHAR(20) DEFAULT "personal"');
          }
        } catch (columnError) {
          console.error('Error checking/adding payment info columns:', columnError);
        }
      }

      const fields = [];
      const values = [];

      // Build the SET clause dynamically based on updateData
      Object.entries(updateData).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        fields.push(`${snakeKey} = ?`);
        values.push(value);
      });

      values.push(id); // Add ID for the WHERE clause

      await query(
        `UPDATE users SET ${fields.join(', ')} WHERE id = ?`,
        values
      );

      return await this.findById(id);
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Compare password
  static async comparePassword(userId, password) {
    try {
      const [rows] = await query('SELECT password FROM users WHERE id = ?', [userId]);

      if (rows.length === 0) {
        return false;
      }

      const hashedPassword = rows[0].password;
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      console.error('Error comparing password:', error);
      throw error;
    }
  }

  // Update affiliate commission
  static async updateAffiliateCommission(userId, amount) {
    try {
      console.log(`Updating affiliate commission for user ${userId} by ${amount}`);

      // First check if the affiliate_commission column exists
      try {
        const [columns] = await query('SHOW COLUMNS FROM users LIKE "affiliate_commission"');
        if (columns.length === 0) {
          console.error('affiliate_commission column does not exist in users table');
          // Try to add the column if it doesn't exist
          try {
            await query('ALTER TABLE users ADD COLUMN affiliate_commission DECIMAL(10, 2) DEFAULT 0');
            console.log('Added affiliate_commission column to users table');
          } catch (alterError) {
            console.error('Error adding affiliate_commission column:', alterError);
            return null;
          }
        }
      } catch (columnError) {
        console.error('Error checking affiliate_commission column:', columnError);
        return null;
      }

      // Make sure amount is a valid number
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount)) {
        console.error('Invalid amount provided:', amount);
        return null;
      }

      // First get the current commission value
      const [currentCommission] = await query(
        'SELECT affiliate_commission FROM users WHERE id = ?',
        [userId]
      );

      console.log('Current commission:', currentCommission[0]?.affiliate_commission || 0);

      // Update the user's affiliate commission
      try {
        // Use direct value assignment instead of increment to avoid potential issues
        const newCommission = (parseFloat(currentCommission[0]?.affiliate_commission) || 0) + numericAmount;

        await query(
          'UPDATE users SET affiliate_commission = ? WHERE id = ?',
          [newCommission, userId]
        );
        console.log(`Successfully updated affiliate commission for user ${userId} to ${newCommission}`);
      } catch (updateError) {
        console.error('Error updating affiliate commission:', updateError);
        return null;
      }

      // Return the updated user
      try {
        return await this.findById(userId);
      } catch (findError) {
        console.error('Error finding user after update:', findError);
        return null;
      }
    } catch (error) {
      console.error('Error in updateAffiliateCommission:', error);
      return null;
    }
  }

  // Update withdrawn commission
  static async updateWithdrawnCommission(userId, amount) {
    try {
      console.log(`Updating withdrawn commission for user ${userId} by ${amount}`);

      // First check if the withdrawn_commission column exists
      try {
        const [columns] = await query('SHOW COLUMNS FROM users LIKE "withdrawn_commission"');
        if (columns.length === 0) {
          console.error('withdrawn_commission column does not exist in users table');
          // Try to add the column if it doesn't exist
          try {
            await query('ALTER TABLE users ADD COLUMN withdrawn_commission DECIMAL(10, 2) DEFAULT 0');
            console.log('Added withdrawn_commission column to users table');
          } catch (alterError) {
            console.error('Error adding withdrawn_commission column:', alterError);
            return null;
          }
        }
      } catch (columnError) {
        console.error('Error checking withdrawn_commission column:', columnError);
        return null;
      }

      // Make sure amount is a valid number
      const numericAmount = parseFloat(amount);
      if (isNaN(numericAmount)) {
        console.error('Invalid amount provided:', amount);
        return null;
      }

      // Update the user's withdrawn commission
      try {
        await query(
          'UPDATE users SET withdrawn_commission = COALESCE(withdrawn_commission, 0) + ? WHERE id = ?',
          [numericAmount, userId]
        );
        console.log(`Successfully updated withdrawn commission for user ${userId}`);
      } catch (updateError) {
        console.error('Error updating withdrawn commission:', updateError);
        return null;
      }

      // Return the updated user
      try {
        return await this.findById(userId);
      } catch (findError) {
        console.error('Error finding user after update:', findError);
        return null;
      }
    } catch (error) {
      console.error('Error in updateWithdrawnCommission:', error);
      return null;
    }
  }

  // MongoDB-style find method
  static async find(criteria = {}) {
    try {
      console.log('Finding users with criteria:', JSON.stringify(criteria));

      // Handle MongoDB-style $or operator
      if (criteria.$or) {
        const orConditions = [];
        const params = [];

        criteria.$or.forEach(condition => {
          const subConditions = [];

          Object.entries(condition).forEach(([key, value]) => {
            // Handle special operators like $gt
            if (key === 'affiliateCommission' && typeof value === 'object' && value.$gt !== undefined) {
              subConditions.push('affiliate_commission > ?');
              params.push(value.$gt);
            } else if (key === 'withdrawnCommission' && typeof value === 'object' && value.$gt !== undefined) {
              subConditions.push('withdrawn_commission > ?');
              params.push(value.$gt);
            } else {
              // Convert camelCase to snake_case
              const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
              subConditions.push(`${snakeKey} = ?`);
              params.push(value);
            }
          });

          if (subConditions.length > 0) {
            orConditions.push(`(${subConditions.join(' AND ')})`);
          }
        });

        if (orConditions.length === 0) {
          console.log('No valid conditions found in $or criteria');
          return [];
        }

        const sql = `SELECT * FROM users WHERE ${orConditions.join(' OR ')}`;
        console.log('Executing SQL query:', sql, 'with params:', params);

        const [rows] = await query(sql, params);
        console.log('Query returned rows:', rows.length);

        // Convert snake_case to camelCase for all results
        const users = rows.map(row => this._formatUser(row));

        // Add MongoDB-compatible methods for backward compatibility
        users.select = function() { return this; };

        return users;
      }

      // Handle direct criteria
      let sql = 'SELECT * FROM users';
      const params = [];
      const conditions = [];

      Object.entries(criteria).forEach(([key, value]) => {
        // Convert camelCase to snake_case
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        conditions.push(`${snakeKey} = ?`);
        params.push(value);
      });

      if (conditions.length > 0) {
        sql += ` WHERE ${conditions.join(' AND ')}`;
      }

      console.log('Executing SQL query:', sql, 'with params:', params);
      const [rows] = await query(sql, params);
      console.log('Query returned rows:', rows.length);

      // Convert snake_case to camelCase for all results
      const users = rows.map(row => this._formatUser(row));

      // Add MongoDB-compatible methods for backward compatibility
      users.select = function() { return this; };

      return users;
    } catch (error) {
      console.error('Error finding users:', error);
      throw error;
    }
  }

  // Add a pending payment record without a license key
  static async addPendingPayment(userId, plan, paymentId) {
    try {
      const LicenseKey = require('./LicenseKey');

      // Create a new license key record with empty key
      return await LicenseKey.create({
        userId,
        paymentId,
        key: '', // Empty license key - will be filled by admin
        plan,
        status: 'pending',
        paymentStatus: 'pending'
      });
    } catch (error) {
      console.error('Error adding pending payment:', error);
      throw error;
    }
  }

  // Add or update a license key
  static async addOrUpdateLicenseKey(userId, licenseKey, plan, paymentId, paymentStatus = 'approved') {
    try {
      console.log('Adding or updating license key for user:', userId);
      const LicenseKey = require('./LicenseKey');

      // Check if a license key entry already exists for this payment
      const existingLicense = await LicenseKey.findByPaymentId(paymentId);

      if (existingLicense) {
        console.log('Updating existing license key:', existingLicense.id);
        // Update existing license
        const updatedLicense = await LicenseKey.update(existingLicense.id, {
          license_key: licenseKey,
          status: 'active',
          payment_status: paymentStatus
        });

        console.log('License key updated successfully:', updatedLicense.id, 'Status:', updatedLicense.status, 'Payment Status:', updatedLicense.paymentStatus);
        return updatedLicense;
      } else {
        console.log('Creating new license key for payment:', paymentId);
        // Create a new license key record
        const newLicense = await LicenseKey.create({
          userId,
          paymentId,
          key: licenseKey,
          plan,
          status: 'active',
          paymentStatus
        });

        console.log('New license key created successfully:', newLicense.id, 'Status:', newLicense.status, 'Payment Status:', newLicense.paymentStatus);
        return newLicense;
      }
    } catch (error) {
      console.error('Error adding or updating license key:', error);
      throw error;
    }
  }

  // Get license keys for a user
  static async getLicenseKeys(userId) {
    try {
      console.log('Getting license keys for user:', userId);
      const LicenseKey = require('./LicenseKey');

      // Get all license keys for this user
      return await LicenseKey.findByUserId(userId);
    } catch (error) {
      console.error('Error getting license keys:', error);
      throw error;
    }
  }

  // Get user with license keys
  static async findByIdWithLicenseKeys(userId) {
    try {
      console.log('Finding user with license keys by ID:', userId);

      // Get the user
      const user = await this.findById(userId);

      if (!user) {
        return null;
      }

      // Get the license keys
      const licenseKeys = await this.getLicenseKeys(userId);

      // Add the license keys to the user object
      user.licenseKeys = licenseKeys || [];

      return user;
    } catch (error) {
      console.error('Error finding user with license keys:', error);
      throw error;
    }
  }
}

module.exports = User;
