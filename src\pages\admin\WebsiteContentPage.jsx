import React, { useState, useEffect } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import {
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  ArrowPathIcon,
  CloudArrowUpIcon,
  PlusIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  ClockIcon,
  DocumentDuplicateIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { Tab } from '@headlessui/react';
import ContentSectionEditor from '../../components/admin/ContentSectionEditor';
import FeatureEditor from '../../components/admin/FeatureEditor';
import FAQEditor from '../../components/admin/FAQEditor';
import TestimonialEditor from '../../components/admin/TestimonialEditor';
import VideoUploader from '../../components/admin/VideoUploader';

const WebsiteContentPage = () => {
  const [websiteContent, setWebsiteContent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [activeTab, setActiveTab] = useState(0);
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [versionHistory, setVersionHistory] = useState([]);
  const [isVersionHistoryModalOpen, setIsVersionHistoryModalOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isBulkEditMode, setIsBulkEditMode] = useState(false);
  const [selectedItems, setSelectedItems] = useState([]);
  const [isLoadingVersions, setIsLoadingVersions] = useState(false);

  // Video management state
  const [tutorialVideos, setTutorialVideos] = useState([]);
  const [demoVideos, setDemoVideos] = useState([]);
  const [selectedVideoCategory, setSelectedVideoCategory] = useState('tutorial');

  useEffect(() => {
    fetchWebsiteContent();
  }, []);

  const fetchWebsiteContent = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');

      if (!token) {
        console.error('No admin token found in localStorage');
        setError('Authentication error. Please log in again.');
        setIsLoading(false);
        return;
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/website-content/current`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch website content: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setWebsiteContent(data.data);

      // Initialize videos if they exist in the data
      if (data.data.videos) {
        const tutVideos = data.data.videos.filter(v => v.category === 'tutorial') || [];
        const demVideos = data.data.videos.filter(v => v.category === 'demo') || [];
        setTutorialVideos(tutVideos);
        setDemoVideos(demVideos);
      } else {
        // Initialize with empty arrays if no videos exist yet
        setTutorialVideos([]);
        setDemoVideos([]);
      }
    } catch (err) {
      console.error('Error fetching website content:', err);
      setError(`Failed to load website content. ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveContent = async () => {
    setIsSaving(true);
    setError(null);
    setSuccessMessage('');

    try {
      const token = localStorage.getItem('adminToken');

      if (!token) {
        console.error('No admin token found in localStorage');
        setError('Authentication error. Please log in again.');
        setIsSaving(false);
        return;
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/website-content/update`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contentId: websiteContent._id,
          sections: websiteContent.sections,
          features: websiteContent.features,
          faqs: websiteContent.faqs,
          testimonials: websiteContent.testimonials,
          videos: [...tutorialVideos, ...demoVideos]
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to save website content: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setWebsiteContent(data.data);
      setSuccessMessage('Content saved successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err) {
      console.error('Error saving website content:', err);
      setError(`Failed to save website content. ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  const handlePublishContent = async () => {
    setIsPublishing(true);
    setError(null);
    setSuccessMessage('');

    try {
      const token = localStorage.getItem('adminToken');

      if (!token) {
        console.error('No admin token found in localStorage');
        setError('Authentication error. Please log in again.');
        setIsPublishing(false);
        return;
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/website-content/publish`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          contentId: websiteContent._id
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to publish website content: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setWebsiteContent(data.data);
      setSuccessMessage('Content published successfully! The changes are now live on the website.');

      // Clear success message after 5 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 5000);
    } catch (err) {
      console.error('Error publishing website content:', err);
      setError(`Failed to publish website content. ${err.message}`);
    } finally {
      setIsPublishing(false);
    }
  };

  // Update section handlers
  const handleUpdateSection = (updatedSection) => {
    setWebsiteContent(prevContent => ({
      ...prevContent,
      sections: prevContent.sections.map(section =>
        section.sectionId === updatedSection.sectionId ? updatedSection : section
      )
    }));
  };

  const handleAddSection = () => {
    const newSection = {
      sectionId: `section-${Date.now()}`,
      sectionName: 'New Section',
      title: 'New Section Title',
      subtitle: 'New section subtitle goes here',
      content: 'New section content goes here',
      order: websiteContent.sections.length + 1,
      isActive: true
    };

    setWebsiteContent(prevContent => ({
      ...prevContent,
      sections: [...prevContent.sections, newSection]
    }));
  };

  const handleDeleteSection = (sectionId) => {
    if (window.confirm('Are you sure you want to delete this section? This cannot be undone.')) {
      setWebsiteContent(prevContent => ({
        ...prevContent,
        sections: prevContent.sections.filter(section => section.sectionId !== sectionId)
      }));
    }
  };

  // Feature handlers
  const handleUpdateFeature = (index, updatedFeature) => {
    setWebsiteContent(prevContent => {
      const newFeatures = [...prevContent.features];
      newFeatures[index] = updatedFeature;
      return {
        ...prevContent,
        features: newFeatures
      };
    });
  };

  const handleAddFeature = () => {
    const newFeature = {
      title: 'New Feature',
      description: 'Description of the new feature',
      icon: 'fas fa-star',
      order: websiteContent.features.length + 1,
      isActive: true
    };

    setWebsiteContent(prevContent => ({
      ...prevContent,
      features: [...prevContent.features, newFeature]
    }));
  };

  const handleDeleteFeature = (index) => {
    if (window.confirm('Are you sure you want to delete this feature? This cannot be undone.')) {
      setWebsiteContent(prevContent => ({
        ...prevContent,
        features: prevContent.features.filter((_, i) => i !== index)
      }));
    }
  };

  // FAQ handlers
  const handleUpdateFAQ = (index, updatedFAQ) => {
    setWebsiteContent(prevContent => {
      const newFAQs = [...prevContent.faqs];
      newFAQs[index] = updatedFAQ;
      return {
        ...prevContent,
        faqs: newFAQs
      };
    });
  };

  const handleAddFAQ = () => {
    const newFAQ = {
      question: 'New Question?',
      answer: 'Answer to the new question.',
      order: websiteContent.faqs.length + 1,
      isActive: true
    };

    setWebsiteContent(prevContent => ({
      ...prevContent,
      faqs: [...prevContent.faqs, newFAQ]
    }));
  };

  const handleDeleteFAQ = (index) => {
    if (window.confirm('Are you sure you want to delete this FAQ? This cannot be undone.')) {
      setWebsiteContent(prevContent => ({
        ...prevContent,
        faqs: prevContent.faqs.filter((_, i) => i !== index)
      }));
    }
  };

  // Testimonial handlers
  const handleUpdateTestimonial = (index, updatedTestimonial) => {
    setWebsiteContent(prevContent => {
      const newTestimonials = [...prevContent.testimonials];
      newTestimonials[index] = updatedTestimonial;
      return {
        ...prevContent,
        testimonials: newTestimonials
      };
    });
  };

  const handleAddTestimonial = () => {
    const newTestimonial = {
      name: 'New Customer',
      role: 'Customer',
      content: 'This is a testimonial from a new customer.',
      rating: 5,
      order: websiteContent.testimonials.length + 1,
      isActive: true
    };

    setWebsiteContent(prevContent => ({
      ...prevContent,
      testimonials: [...prevContent.testimonials, newTestimonial]
    }));
  };

  const handleDeleteTestimonial = (index) => {
    if (window.confirm('Are you sure you want to delete this testimonial? This cannot be undone.')) {
      setWebsiteContent(prevContent => ({
        ...prevContent,
        testimonials: prevContent.testimonials.filter((_, i) => i !== index)
      }));
    }
  };

  // Video management handlers
  const handleAddVideo = (videoData) => {
    const newVideo = {
      id: `video-${Date.now()}`,
      title: 'New Video',
      description: 'Description for this video',
      videoUrl: videoData.videoUrl,
      fileName: videoData.fileName,
      fileSize: videoData.fileSize,
      fileType: videoData.fileType,
      category: selectedVideoCategory,
      order: selectedVideoCategory === 'tutorial' ? tutorialVideos.length + 1 : demoVideos.length + 1,
      isActive: true,
      uploadedAt: new Date().toISOString()
    };

    if (selectedVideoCategory === 'tutorial') {
      setTutorialVideos([...tutorialVideos, newVideo]);
    } else {
      setDemoVideos([...demoVideos, newVideo]);
    }
  };

  const handleUpdateVideo = (videoId, updatedData) => {
    if (selectedVideoCategory === 'tutorial') {
      setTutorialVideos(prevVideos =>
        prevVideos.map(video =>
          video.id === videoId ? { ...video, ...updatedData } : video
        )
      );
    } else {
      setDemoVideos(prevVideos =>
        prevVideos.map(video =>
          video.id === videoId ? { ...video, ...updatedData } : video
        )
      );
    }
  };

  const handleDeleteVideo = (videoId) => {
    if (window.confirm('Are you sure you want to delete this video? This cannot be undone.')) {
      if (selectedVideoCategory === 'tutorial') {
        setTutorialVideos(prevVideos =>
          prevVideos.filter(video => video.id !== videoId)
        );
      } else {
        setDemoVideos(prevVideos =>
          prevVideos.filter(video => video.id !== videoId)
        );
      }
    }
  };

  // Preview functionality
  const handleOpenPreview = () => {
    setIsPreviewModalOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewModalOpen(false);
  };

  // Version history functionality
  const fetchVersionHistory = async () => {
    setIsLoadingVersions(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');

      if (!token) {
        console.error('No admin token found in localStorage');
        setError('Authentication error. Please log in again.');
        setIsLoadingVersions(false);
        return;
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/website-content/versions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch version history: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setVersionHistory(data.data);
      setIsVersionHistoryModalOpen(true);
    } catch (err) {
      console.error('Error fetching version history:', err);
      setError(`Failed to load version history. ${err.message}`);
    } finally {
      setIsLoadingVersions(false);
    }
  };

  const handleRestoreVersion = async (versionId) => {
    if (!window.confirm('Are you sure you want to restore this version? Current unsaved changes will be lost.')) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const token = localStorage.getItem('adminToken');

      if (!token) {
        console.error('No admin token found in localStorage');
        setError('Authentication error. Please log in again.');
        setIsLoading(false);
        return;
      }

      const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:5001';
      const response = await fetch(`${apiUrl}/api/website-content/restore/${versionId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to restore version: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      setWebsiteContent(data.data);
      setSuccessMessage('Version restored successfully!');
      setIsVersionHistoryModalOpen(false);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (err) {
      console.error('Error restoring version:', err);
      setError(`Failed to restore version. ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Search functionality
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const filterContentBySearch = (items, type) => {
    if (!searchTerm) return items;

    return items.filter(item => {
      switch (type) {
        case 'sections':
          return (
            item.sectionName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (item.subtitle && item.subtitle.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (item.content && item.content.toLowerCase().includes(searchTerm.toLowerCase()))
          );
        case 'features':
          return (
            item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.description.toLowerCase().includes(searchTerm.toLowerCase())
          );
        case 'faqs':
          return (
            item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
            item.answer.toLowerCase().includes(searchTerm.toLowerCase())
          );
        case 'testimonials':
          return (
            item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (item.role && item.role.toLowerCase().includes(searchTerm.toLowerCase())) ||
            item.content.toLowerCase().includes(searchTerm.toLowerCase())
          );
        default:
          return true;
      }
    });
  };

  // Bulk edit functionality
  const toggleBulkEditMode = () => {
    setIsBulkEditMode(!isBulkEditMode);
    setSelectedItems([]);
  };

  const handleSelectItem = (id, type, isSelected) => {
    if (isSelected) {
      setSelectedItems([...selectedItems, { id, type }]);
    } else {
      setSelectedItems(selectedItems.filter(item => !(item.id === id && item.type === type)));
    }
  };

  const handleBulkDelete = () => {
    if (!selectedItems.length) return;

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected items? This cannot be undone.`)) {
      const newContent = { ...websiteContent };

      // Group selected items by type
      const itemsByType = selectedItems.reduce((acc, item) => {
        if (!acc[item.type]) acc[item.type] = [];
        acc[item.type].push(item.id);
        return acc;
      }, {});

      // Process each type
      if (itemsByType.sections) {
        newContent.sections = newContent.sections.filter(section =>
          !itemsByType.sections.includes(section.sectionId)
        );
      }

      if (itemsByType.features) {
        newContent.features = newContent.features.filter((_, index) =>
          !itemsByType.features.includes(index)
        );
      }

      if (itemsByType.faqs) {
        newContent.faqs = newContent.faqs.filter((_, index) =>
          !itemsByType.faqs.includes(index)
        );
      }

      if (itemsByType.testimonials) {
        newContent.testimonials = newContent.testimonials.filter((_, index) =>
          !itemsByType.testimonials.includes(index)
        );
      }

      setWebsiteContent(newContent);
      setSelectedItems([]);
      setSuccessMessage(`${selectedItems.length} items deleted successfully!`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    }
  };

  const handleBulkToggleActive = (setActive) => {
    if (!selectedItems.length) return;

    const newContent = { ...websiteContent };

    // Group selected items by type
    const itemsByType = selectedItems.reduce((acc, item) => {
      if (!acc[item.type]) acc[item.type] = [];
      acc[item.type].push(item.id);
      return acc;
    }, {});

    // Process each type
    if (itemsByType.sections) {
      newContent.sections = newContent.sections.map(section =>
        itemsByType.sections.includes(section.sectionId)
          ? { ...section, isActive: setActive }
          : section
      );
    }

    if (itemsByType.features) {
      newContent.features = newContent.features.map((feature, index) =>
        itemsByType.features.includes(index)
          ? { ...feature, isActive: setActive }
          : feature
      );
    }

    if (itemsByType.faqs) {
      newContent.faqs = newContent.faqs.map((faq, index) =>
        itemsByType.faqs.includes(index)
          ? { ...faq, isActive: setActive }
          : faq
      );
    }

    if (itemsByType.testimonials) {
      newContent.testimonials = newContent.testimonials.map((testimonial, index) =>
        itemsByType.testimonials.includes(index)
          ? { ...testimonial, isActive: setActive }
          : testimonial
      );
    }

    setWebsiteContent(newContent);
    setSuccessMessage(`${selectedItems.length} items ${setActive ? 'activated' : 'deactivated'} successfully!`);

    // Clear success message after 3 seconds
    setTimeout(() => {
      setSuccessMessage('');
    }, 3000);
  };

  return (
    <AdminLayout>
      <div className="py-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Website Content Management</h1>
            <p className="mt-1 text-sm text-gray-500">
              Edit and manage the content displayed on the Meta Master website
            </p>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={handleOpenPreview}
              disabled={isSaving || isPublishing}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <EyeIcon className="-ml-1 mr-2 h-5 w-5" />
              Preview
            </button>
            <button
              onClick={fetchVersionHistory}
              disabled={isSaving || isPublishing || isLoadingVersions}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isLoadingVersions ? (
                <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-5 w-5" />
              ) : (
                <ClockIcon className="-ml-1 mr-2 h-5 w-5" />
              )}
              Version History
            </button>
            <button
              onClick={handleSaveContent}
              disabled={isSaving || isPublishing}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {isSaving ? (
                <>
                  <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-5 w-5" />
                  Saving...
                </>
              ) : (
                <>
                  <CheckIcon className="-ml-1 mr-2 h-5 w-5" />
                  Save Changes
                </>
              )}
            </button>
            <button
              onClick={handlePublishContent}
              disabled={isSaving || isPublishing}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              {isPublishing ? (
                <>
                  <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-5 w-5" />
                  Publishing...
                </>
              ) : (
                <>
                  <CloudArrowUpIcon className="-ml-1 mr-2 h-5 w-5" />
                  Publish to Website
                </>
              )}
            </button>
          </div>
        </div>

        {successMessage && (
          <div className="mt-4 bg-green-50 border-l-4 border-green-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <CheckIcon className="h-5 w-5 text-green-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-700">{successMessage}</p>
              </div>
            </div>
          </div>
        )}

        {error && (
          <div className="mt-4 bg-red-50 border-l-4 border-red-500 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <XMarkIcon className="h-5 w-5 text-red-500" />
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center mt-8">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : websiteContent ? (
          <div className="mt-6">
            <Tab.Group selectedIndex={activeTab} onChange={setActiveTab}>
              <Tab.List className="flex space-x-1 rounded-xl bg-blue-900/20 p-1">
                <Tab
                  className={({ selected }) =>
                    `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-blue-700
                    ${selected ? 'bg-white shadow' : 'text-blue-100 hover:bg-white/[0.12] hover:text-blue-600'}`
                  }
                >
                  Sections
                </Tab>
                <Tab
                  className={({ selected }) =>
                    `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-blue-700
                    ${selected ? 'bg-white shadow' : 'text-blue-100 hover:bg-white/[0.12] hover:text-blue-600'}`
                  }
                >
                  Features
                </Tab>
                <Tab
                  className={({ selected }) =>
                    `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-blue-700
                    ${selected ? 'bg-white shadow' : 'text-blue-100 hover:bg-white/[0.12] hover:text-blue-600'}`
                  }
                >
                  FAQs
                </Tab>
                <Tab
                  className={({ selected }) =>
                    `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-blue-700
                    ${selected ? 'bg-white shadow' : 'text-blue-100 hover:bg-white/[0.12] hover:text-blue-600'}`
                  }
                >
                  Testimonials
                </Tab>
                <Tab
                  className={({ selected }) =>
                    `w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-blue-700
                    ${selected ? 'bg-white shadow' : 'text-blue-100 hover:bg-white/[0.12] hover:text-blue-600'}`
                  }
                >
                  Videos
                </Tab>
              </Tab.List>
              <Tab.Panels className="mt-2">
                <Tab.Panel className="rounded-xl bg-white p-3">
                  <div className="flex justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <div className="relative">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        <input
                          type="text"
                          placeholder="Search sections..."
                          value={searchTerm}
                          onChange={handleSearch}
                          className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        />
                      </div>
                      <button
                        onClick={toggleBulkEditMode}
                        className={`inline-flex items-center px-3 py-2 border ${
                          isBulkEditMode ? 'border-red-300 bg-red-50 text-red-700' : 'border-gray-300 bg-white text-gray-700'
                        } rounded-md text-sm font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
                      >
                        {isBulkEditMode ? 'Cancel Bulk Edit' : 'Bulk Edit'}
                      </button>
                      {isBulkEditMode && selectedItems.length > 0 && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleBulkToggleActive(true)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            Activate
                          </button>
                          <button
                            onClick={() => handleBulkToggleActive(false)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                          >
                            Deactivate
                          </button>
                          <button
                            onClick={handleBulkDelete}
                            className="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            Delete Selected
                          </button>
                        </div>
                      )}
                    </div>
                    <button
                      onClick={handleAddSection}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                      Add Section
                    </button>
                  </div>
                  {isBulkEditMode && (
                    <div className="mb-4 bg-blue-50 p-4 rounded-md">
                      <p className="text-sm text-blue-700">
                        Bulk edit mode: Select multiple items to perform actions on them at once.
                        {selectedItems.length > 0 && ` ${selectedItems.length} items selected.`}
                      </p>
                    </div>
                  )}
                  <div className="space-y-6">
                    {filterContentBySearch(websiteContent.sections, 'sections').map((section) => (
                      <ContentSectionEditor
                        key={section.sectionId}
                        section={section}
                        onUpdate={handleUpdateSection}
                        onDelete={handleDeleteSection}
                        isBulkEditMode={isBulkEditMode}
                        isSelected={selectedItems.some(item => item.id === section.sectionId && item.type === 'sections')}
                        onSelectItem={(isSelected) => handleSelectItem(section.sectionId, 'sections', isSelected)}
                      />
                    ))}
                    {filterContentBySearch(websiteContent.sections, 'sections').length === 0 && (
                      <div className="text-center py-4 text-gray-500">
                        No sections found matching your search.
                      </div>
                    )}
                  </div>
                </Tab.Panel>
                <Tab.Panel className="rounded-xl bg-white p-3">
                  <div className="flex justify-end mb-4">
                    <button
                      onClick={handleAddFeature}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                      Add Feature
                    </button>
                  </div>
                  <div className="space-y-6">
                    {websiteContent.features.map((feature, index) => (
                      <FeatureEditor
                        key={index}
                        feature={feature}
                        index={index}
                        onUpdate={handleUpdateFeature}
                        onDelete={handleDeleteFeature}
                      />
                    ))}
                  </div>
                </Tab.Panel>
                <Tab.Panel className="rounded-xl bg-white p-3">
                  <div className="flex justify-end mb-4">
                    <button
                      onClick={handleAddFAQ}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                      Add FAQ
                    </button>
                  </div>
                  <div className="space-y-6">
                    {websiteContent.faqs.map((faq, index) => (
                      <FAQEditor
                        key={index}
                        faq={faq}
                        index={index}
                        onUpdate={handleUpdateFAQ}
                        onDelete={handleDeleteFAQ}
                      />
                    ))}
                  </div>
                </Tab.Panel>
                <Tab.Panel className="rounded-xl bg-white p-3">
                  <div className="flex justify-end mb-4">
                    <button
                      onClick={handleAddTestimonial}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                      <PlusIcon className="-ml-1 mr-2 h-4 w-4" />
                      Add Testimonial
                    </button>
                  </div>
                  <div className="space-y-6">
                    {websiteContent.testimonials.map((testimonial, index) => (
                      <TestimonialEditor
                        key={index}
                        testimonial={testimonial}
                        index={index}
                        onUpdate={handleUpdateTestimonial}
                        onDelete={handleDeleteTestimonial}
                      />
                    ))}
                  </div>
                </Tab.Panel>
                <Tab.Panel className="rounded-xl bg-white p-3">
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-4">
                      <div>
                        <h2 className="text-lg font-medium text-gray-900">Video Management</h2>
                        <p className="text-sm text-gray-500">Upload and manage videos for the Tutorial and Demo sections</p>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => setSelectedVideoCategory('tutorial')}
                          className={`px-4 py-2 text-sm font-medium rounded-md ${
                            selectedVideoCategory === 'tutorial'
                              ? 'bg-indigo-100 text-indigo-700'
                              : 'bg-white text-gray-700 border border-gray-300'
                          }`}
                        >
                          Tutorial Videos
                        </button>
                        <button
                          onClick={() => setSelectedVideoCategory('demo')}
                          className={`px-4 py-2 text-sm font-medium rounded-md ${
                            selectedVideoCategory === 'demo'
                              ? 'bg-indigo-100 text-indigo-700'
                              : 'bg-white text-gray-700 border border-gray-300'
                          }`}
                        >
                          Demo Videos
                        </button>
                      </div>
                    </div>

                    <div className="bg-gray-50 p-4 rounded-lg mb-6">
                      <h3 className="text-md font-medium text-gray-900 mb-2">Upload New Video</h3>
                      <VideoUploader
                        onVideoUpload={handleAddVideo}
                        label={`Upload ${selectedVideoCategory === 'tutorial' ? 'Tutorial' : 'Demo'} Video`}
                        id={`${selectedVideoCategory}-video-upload`}
                      />
                    </div>

                    <div>
                      <h3 className="text-md font-medium text-gray-900 mb-4">
                        {selectedVideoCategory === 'tutorial' ? 'Tutorial' : 'Demo'} Videos ({
                          selectedVideoCategory === 'tutorial' ? tutorialVideos.length : demoVideos.length
                        })
                      </h3>

                      {selectedVideoCategory === 'tutorial' && tutorialVideos.length === 0 && (
                        <div className="text-center py-8 bg-gray-50 rounded-lg">
                          <p className="text-gray-500">No tutorial videos uploaded yet.</p>
                          <p className="text-sm text-gray-400 mt-1">Upload videos to display in the Tutorial section.</p>
                        </div>
                      )}

                      {selectedVideoCategory === 'demo' && demoVideos.length === 0 && (
                        <div className="text-center py-8 bg-gray-50 rounded-lg">
                          <p className="text-gray-500">No demo videos uploaded yet.</p>
                          <p className="text-sm text-gray-400 mt-1">Upload videos to display in the Demo section.</p>
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {(selectedVideoCategory === 'tutorial' ? tutorialVideos : demoVideos).map((video) => (
                          <div key={video.id} className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                            <div className="aspect-video bg-gray-100 relative">
                              <video
                                src={video.videoUrl}
                                className="w-full h-full object-cover"
                                controls
                              />
                            </div>
                            <div className="p-4">
                              <div className="mb-3">
                                <input
                                  type="text"
                                  value={video.title || ''}
                                  onChange={(e) => handleUpdateVideo(video.id, { title: e.target.value })}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                  placeholder="Video title"
                                />
                              </div>
                              <div className="mb-3">
                                <textarea
                                  value={video.description || ''}
                                  onChange={(e) => handleUpdateVideo(video.id, { description: e.target.value })}
                                  rows={3}
                                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                  placeholder="Video description"
                                />
                              </div>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center">
                                  <input
                                    type="checkbox"
                                    id={`active-${video.id}`}
                                    checked={video.isActive}
                                    onChange={(e) => handleUpdateVideo(video.id, { isActive: e.target.checked })}
                                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                  />
                                  <label htmlFor={`active-${video.id}`} className="ml-2 block text-sm text-gray-900">
                                    Active
                                  </label>
                                </div>
                                <button
                                  onClick={() => handleDeleteVideo(video.id)}
                                  className="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                >
                                  <TrashIcon className="h-4 w-4 mr-1" />
                                  Delete
                                </button>
                              </div>
                              <div className="mt-2 text-xs text-gray-500">
                                <p>File: {video.fileName}</p>
                                <p>Size: {(video.fileSize / (1024 * 1024)).toFixed(2)} MB</p>
                                <p>Uploaded: {new Date(video.uploadedAt).toLocaleString()}</p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </Tab.Panel>
              </Tab.Panels>
            </Tab.Group>
          </div>
        ) : (
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-md p-6 text-center">
            <p className="text-gray-500">No website content found. Please refresh the page or contact support.</p>
          </div>
        )}

        {/* Preview Modal */}
        {isPreviewModalOpen && websiteContent && (
          <div className="fixed z-10 inset-0 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>

              <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Content Preview
                      </h3>
                      <div className="mt-4 border-t border-gray-200 pt-4">
                        <div className="bg-gray-100 p-4 rounded-lg overflow-auto max-h-[70vh]">
                          <h2 className="text-xl font-bold mb-4">Website Sections</h2>
                          {websiteContent.sections.filter(s => s.isActive).map((section) => (
                            <div key={section.sectionId} className="mb-8 bg-white p-4 rounded-lg shadow">
                              <h3 className="text-lg font-semibold">{section.sectionName}</h3>
                              <h4 className="text-xl font-bold mt-2">{section.title}</h4>
                              {section.subtitle && <p className="text-gray-600 mt-1">{section.subtitle}</p>}
                              {section.content && <div className="mt-3 whitespace-pre-line">{section.content}</div>}
                              {section.imageUrl && (
                                <div className="mt-3">
                                  <img src={section.imageUrl} alt={section.title} className="max-h-40 object-contain" />
                                </div>
                              )}
                              {section.buttonText && (
                                <div className="mt-3">
                                  <span className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600">
                                    {section.buttonText}
                                  </span>
                                  <span className="ml-2 text-sm text-gray-500">→ {section.buttonUrl}</span>
                                </div>
                              )}
                            </div>
                          ))}

                          <h2 className="text-xl font-bold mb-4 mt-8">Features</h2>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {websiteContent.features.filter(f => f.isActive).map((feature, index) => (
                              <div key={index} className="bg-white p-4 rounded-lg shadow">
                                <h3 className="text-lg font-semibold">{feature.title}</h3>
                                <p className="mt-2">{feature.description}</p>
                                {feature.icon && (
                                  <div className="mt-2">
                                    <i className={feature.icon} style={{ fontSize: '24px' }}></i>
                                  </div>
                                )}
                                {feature.imageUrl && (
                                  <div className="mt-2">
                                    <img src={feature.imageUrl} alt={feature.title} className="max-h-32 object-contain" />
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>

                          <h2 className="text-xl font-bold mb-4 mt-8">FAQs</h2>
                          <div className="space-y-4">
                            {websiteContent.faqs.filter(f => f.isActive).map((faq, index) => (
                              <div key={index} className="bg-white p-4 rounded-lg shadow">
                                <h3 className="text-lg font-semibold">{faq.question}</h3>
                                <p className="mt-2 whitespace-pre-line">{faq.answer}</p>
                              </div>
                            ))}
                          </div>

                          <h2 className="text-xl font-bold mb-4 mt-8">Testimonials</h2>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {websiteContent.testimonials.filter(t => t.isActive).map((testimonial, index) => (
                              <div key={index} className="bg-white p-4 rounded-lg shadow">
                                <div className="flex items-center mb-3">
                                  {testimonial.imageUrl ? (
                                    <img src={testimonial.imageUrl} alt={testimonial.name} className="h-12 w-12 rounded-full mr-3 object-cover" />
                                  ) : (
                                    <div className="h-12 w-12 rounded-full bg-gray-200 mr-3 flex items-center justify-center">
                                      <span className="text-gray-500 font-semibold">{testimonial.name.charAt(0)}</span>
                                    </div>
                                  )}
                                  <div>
                                    <h3 className="font-semibold">{testimonial.name}</h3>
                                    {testimonial.role && <p className="text-sm text-gray-500">{testimonial.role}</p>}
                                  </div>
                                </div>
                                <p className="italic">"{testimonial.content}"</p>
                                <div className="flex mt-2">
                                  {[1, 2, 3, 4, 5].map((star) => (
                                    <svg
                                      key={star}
                                      className={`h-5 w-5 ${star <= testimonial.rating ? 'text-yellow-400' : 'text-gray-300'}`}
                                      xmlns="http://www.w3.org/2000/svg"
                                      viewBox="0 0 20 20"
                                      fill="currentColor"
                                    >
                                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                    </svg>
                                  ))}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={handleClosePreview}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Version History Modal */}
        {isVersionHistoryModalOpen && (
          <div className="fixed z-10 inset-0 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
              <div className="fixed inset-0 transition-opacity" aria-hidden="true">
                <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
              </div>

              <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

              <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                  <div className="sm:flex sm:items-start">
                    <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Version History
                      </h3>
                      <div className="mt-4 border-t border-gray-200 pt-4">
                        {versionHistory.length === 0 ? (
                          <p className="text-gray-500 text-center py-4">No version history found.</p>
                        ) : (
                          <ul className="divide-y divide-gray-200">
                            {versionHistory.map((version) => (
                              <li key={version._id} className="py-4">
                                <div className="flex justify-between">
                                  <div>
                                    <p className="text-sm font-medium text-gray-900">
                                      Version {version.version}
                                      {version.isPublished && (
                                        <span className="ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                          Published
                                        </span>
                                      )}
                                    </p>
                                    <p className="text-sm text-gray-500">
                                      {new Date(version.updatedAt).toLocaleString()}
                                    </p>
                                  </div>
                                  <button
                                    onClick={() => handleRestoreVersion(version._id)}
                                    className="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                  >
                                    Restore
                                  </button>
                                </div>
                              </li>
                            ))}
                          </ul>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                    onClick={() => setIsVersionHistoryModalOpen(false)}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  );
};

export default WebsiteContentPage;
