import { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useForm } from 'react-hook-form'
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'

export default function LoginPage() {
  const { register, handleSubmit, formState: { errors } } = useForm()
  const [isLoading, setIsLoading] = useState(false)
  const [loginError, setLoginError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  // Get redirect information from location state (if coming from checkout)
  const { returnTo, plan, price } = location.state || {}

  useEffect(() => {
    // Check if already logged in
    const token = localStorage.getItem('userToken')
    if (token) {
      // Handle redirects based on where the user came from
      if (returnTo === '/checkout' && plan && price) {
        navigate('/checkout', { state: { plan, price } })
      } else if (returnTo === '/download') {
        navigate('/download')
      } else {
        navigate('/user/dashboard')
      }
    }
  }, [navigate, returnTo, plan, price])

  const onSubmit = async (data) => {
    setIsLoading(true)
    setLoginError('')

    try {
      console.log('Attempting to login with:', { username: data.username, password: '***' });
      const response = await fetch('/api/users/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          username: data.username,
          password: data.password
        })
      });

      if (!response.ok) {
        console.error('API Response:', response);
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(errorText || 'Login failed');
      }

      const result = await response.json()

      // Store token in localStorage
      localStorage.setItem('userToken', result.token)

      // Handle redirects based on where the user came from
      if (returnTo === '/checkout' && plan && price) {
        navigate('/checkout', { state: { plan, price } })
      } else if (returnTo === '/download') {
        navigate('/download')
      } else {
        navigate('/user/dashboard')
      }
    } catch (error) {
      console.error('Login error:', error)
      setLoginError(error.message || 'Login failed. Please check your credentials.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <section className="py-16 md:py-24 bg-dark min-h-screen">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-md mx-auto bg-dark-light/30 backdrop-blur-sm border border-white/5 rounded-2xl p-8"
        >
          <div className="flex justify-end mb-4">
            <Link
              to="/"
              className="text-primary hover:text-secondary transition-colors flex items-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              Go to Home
            </Link>
          </div>

          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Log In to Your Account
            </h1>
            <p className="text-text mt-2">
              Access your Meta Master dashboard and licenses
            </p>
          </div>

          {loginError && (
            <div className="bg-danger/10 border border-danger/20 text-danger rounded-lg p-4 mb-6">
              {loginError}
            </div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            <div>
              <label htmlFor="username" className="block text-sm font-medium text-light mb-1">
                Username or Email
              </label>
              <input
                type="text"
                autoComplete="username"
                {...register('username', {
                  required: 'Username is required',
                  minLength: { value: 3, message: 'Username must be at least 3 characters' }
                })}
                className={`w-full px-4 py-3 rounded-lg bg-dark-lighter text-text border border-white/10 focus:border-primary transition-colors ${
                  errors.username ? 'border-red-500' : ''
                }`}
                placeholder="Enter your username"
              />
              {errors.username && (
                <p className="mt-1 text-sm text-danger">{errors.username.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-light mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  {...register('password', {
                    required: 'Password is required',
                    minLength: { value: 6, message: 'Password must be at least 6 characters' }
                  })}
                  className={`w-full px-4 py-3 rounded-lg bg-dark-lighter text-text border border-white/10 focus:border-primary transition-colors ${
                    errors.password ? 'border-red-500' : ''
                  }`}
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text hover:text-light"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="mt-1 text-sm text-danger">{errors.password.message}</p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="remember-me"
                  name="remember-me"
                  type="checkbox"
                  className="h-4 w-4 text-primary focus:ring-primary border-border rounded bg-dark"
                />
                <label htmlFor="remember-me" className="ml-2 block text-sm text-text">
                  Remember me
                </label>
              </div>

              <div className="text-sm">
                <Link to="/user/forgot-password" className="text-primary hover:underline">
                  Forgot your password?
                </Link>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-primary to-secondary text-white font-medium py-3 px-4 rounded-lg hover:opacity-90 transition-opacity disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Logging in...' : 'Log In'}
            </button>

            <div className="text-center text-sm text-text">
              Don't have an account?{' '}
              <Link to="/user/register" className="text-primary hover:underline">
                Create an account
              </Link>
            </div>
          </form>
        </motion.div>
      </div>
    </section>
  )
}
