# Server configuration
PORT=5173
NODE_ENV=development

# MongoDB connection string
# For local development:
MONGODB_URI=mongodb://localhost:27017/meta-master
# For production with MongoDB Atlas:
# MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/meta-master?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=meta-master-jwt-secret-change-in-production
JWT_EXPIRES_IN=24h

# Email Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>
